drenso_oidc:
    #default_client: default # The default client, will be aliased to OidcClientInterface
    clients:
        default: # The client name, each client will be aliased to its name (for example, $defaultOidcClient)
            # Required OIDC client configuration
            well_known_url: '%env(OIDC_WELL_KNOWN_URL)%'
            client_id: '%env(OIDC_CLIENT_ID)%'
            client_secret: ''

            # Extra configuration options
            well_known_parser: 'App\Service\IdfedWellKnownParser' # Service id for a custom well-known configuration parser
            well_known_cache_time: 600 # Time in seconds, will only be used when symfony/cache is available
            redirect_route: '%env(APP_BASE_URL)%/login_check'
            #custom_client_headers: []

            code_challenge_method: S256 # Code challenge method, can be null, 'S256' or 'plain'
            #disable_nonce: false # Set to true when nonce verification should not be used
