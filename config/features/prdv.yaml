code: prdv
label: 
enabled: false
early_adopters:
    enabled: false
    label: 
sources: [APP, WEB]
form:
    title: prdv
    fields:
        - name: activation_prdv
          section: Prise de rendez-vous (Disponible pour l'app)
          translatable: false
          type: CheckboxType
          transformer: App\DataTransformers\StringToBooleanTransformer
          options: { label: activation, required: false}

        - name: type_prdv
          section: Prise de rendez-vous (Disponible pour l'app)
          translatable: false
          type: ChoiceType
          options: { label: type, expanded: true, choices: {NATIF: natif, WEBVIEW: webview, ONLY YOU: only_you}}

        - name: solution
          section: Prendre un rendez-vous
          translatable: false
          type: ChoiceType
          options: { label: Solution, expanded: true, choices: {central: central, local: local}}
        
        - name: url_prdv
          section: Prendre un rendez-vous
          translatable: true
          type: TextType
          options: { label: Url, required: false}

        - name: activation_devis
          section: Effectuer un devis
          translatable: false
          type: CheckboxType
          transformer: App\DataTransformers\StringToBooleanTransformer
          options: { label: activation, required: false}

        - name: type_devis
          section: Effectuer un devis
          translatable: false
          type: ChoiceType
          options: { label: Solution, expanded: true, choices: {central: Central, local: local}}
        
        - name: url_devis
          section: Effectuer un devis
          translatable: true
          type: TextType
          options: { label: Url, required: false}