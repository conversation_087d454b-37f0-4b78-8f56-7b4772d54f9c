code: customer
label: customer_relationship
enabled: false
early_adopters:
    enabled: false
    label: 
sources: [APP, WEB]
form:
    title: customer_relationship
    fields:
        - name: activation_customer
          section: customer_relationship
          translatable: false
          type: CheckboxType
          transformer: App\DataTransformers\StringToBooleanTransformer
          options: { label: activation, required: false}

        - name: url_customer
          section: customer_relationship
          translatable: false
          type: UrlType
          options: { label: faq, required: false }
              

        - name: form_customer
          section: customer_relationship
          translatable: false 
          type: ChoiceType
          options: { label: form_customer, choices: {none: Aucun, non-care: Non Care, c1st: Customer1st}}  

        - name: telephone_customer
          section: customer_relationship
          translatable: false
          type: TextType
          options: { label: telephone, required: true}
        
        - name: assistance_activation
          section: assistance_peugeot
          translatable: false
          type: CheckboxType
          transformer: App\DataTransformers\StringToBooleanTransformer
          options: { label: activation, required: false}
        
        - name: assistance_telephone
          section: assistance_peugeot
          translatable: false
          type: TextType
          options: { label: telephone, required: true}

       