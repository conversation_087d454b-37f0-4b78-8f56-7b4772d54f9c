# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
imports:
    - { resource: access_log.yaml }

parameters:
    account:
        used_iam_instance_profile: '%env(bool:USED_IAM_INSTANCE_PROFILE)%'
        access_key: '%env(AWS_ACCESS_KEY)%'
        secret_key: '%env(AWS_SECRET_KEY)%'
        role_arn: '%env(AWS_ROLE_ARN)%'
        region:  'eu-west-1' #'%env(AWS_DEFAULT_REGION)%'
        version: 'latest'
    media_url: "%env(MEDIA_BASE_URL)%"
    media_size: "%env(MAX_MEDIA_SIZE)%"
    mongo_db.app: "%env(MONGO_APP)%"
    mongo_db.database: "%env(MONGO_DATABASE)%"
    mongo_db.datasource: "%env(MONGO_DATASOURCE)%"
    aws_settings_bucket: "%env(S3_SETTINGS_BUCKET)%"
    aws_publish_bucket: "%env(S3_SETTINGS_BUCKET)%"
    settings_url: "%env(S3_SETTINGS_BUCKET_URL)%"
    log_incident_url: "%env(LOG_INCIDENT_URL)%"
    
    
services:
    GuzzleHttp\Client: ~
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $mediaUrl: "%media_url%"
            $mediaSize: "%media_size%"
            $requestStack: '@request_stack'

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
    App\Security\MockUsersRequestMatcher:
        arguments:
            $useMockUsers: false
            $environment: '%env(APP_ENV)%'
    App\Service\MockUserLoaderService:
        arguments:
            $mockUsersFile: "%kernel.project_dir%/config/mock/users_mock.xml"
    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    App\Controller\MediaController:
        arguments:
            $mediaSize: "%env(MAX_MEDIA_SIZE)%"

    App\Twig\AppExtension:
        tags:
            - { name: twig.extension }
            
    App\Service\AwsSdkService:
        arguments:
            - "%account%"
            - '@Aws\Credentials\Credentials'
    
    Aws\Credentials\Credentials:
        arguments:
            - "@=parameter('account')['access_key']"
            - "@=parameter('account')['secret_key']"

    App\Service\MongoAtlasApiConnector:
        $mongoApp: '%mongo_db.app%'
    
    App\Service\MongoAtlasQueryService:
        arguments:
            $database: '%mongo_db.database%'
            $dataSource: '%mongo_db.datasource%'

    App\Service\MongoDBService:
        arguments:
            $mongoUrl: '%env(resolve:MONGO_URL)%'
            $databaseName: '%env(MONGO_DATABASE)%'

    App\Service\MongoDBQueryService:
        arguments:
            $dataSource: '%mongo_db.datasource%'
            $database: '%mongo_db.database%'
            
    App\Service\SettingsService:
        arguments:
            $settingsBucket: '%aws_settings_bucket%'
            $baseUrl: '%settings_url%'

    App\Service\WidgetManager:
        arguments:
            $settingsBucket: '%aws_settings_bucket%'

    App\Service\SiteManager:
        arguments:
            $settingsBucket: '%aws_settings_bucket%'

    App\Service\PublishService:
        arguments:
            $publishBucket: '%aws_publish_bucket%'
            $baseUrl: '%settings_url%'

    App\Service\StaticPageManager:
        arguments:
            $publishBucket: '%aws_publish_bucket%'
            $baseUrl: '%settings_url%'
            $mediaUrl: "%media_url%"
            $mediaBucket: "%env(MEDIA_S3_ID)%"

    App\DataFixtures\Processor\SkipMissingTablesProcessor:
        arguments:
            - '@doctrine.orm.entity_manager'
        tags: ['fidry_alice_data_fixtures.processor']
    App\Service\AccessLogConfigService:
        arguments:
             $params: '@parameter_bag'

    App\EventListener\EntityListener:
        arguments:
            $security: '@security.helper'
            $requestStack: '@request_stack'
            $configService: '@App\Service\AccessLogConfigService'
            $entityManager: '@doctrine.orm.entity_manager'

        tags:
            - { name: doctrine.event_listener, event: preUpdate }
            - { name: doctrine.event_listener, event: prePersist }
            - { name: doctrine.event_listener, event: preRemove }
            - { name: doctrine.event_listener, event: postUpdate }
            - { name: doctrine.event_listener, event: postPersist }
            - { name: doctrine.event_listener, event: postRemove }
            - { name: kernel.event_subscriber }

    App\Service\SingleLogoutService:
        arguments:
            $sloUrl: '%env(PINGFEDERATE_SLO_URL)%'
            $targetResource: '%env(TARGET_RESOURCE)%'

    App\Service\MediaManager:
        arguments:
            $mediaBucket: "%env(MEDIA_S3_ID)%"
            $distributionId: "%env(MEDIA_CLOUD_FRONT_DISTRIBUTION_ID)%"

    App\Service\LogIncidentManager:
        arguments:
            $baseUrl: '%log_incident_url%'

    # Keep uncommented if you want to use the file session handler
    #session.handler.file:
    #    class: Symfony\Component\HttpFoundation\Session\Storage\Handler\NativeFileSessionHandler
    #    arguments:
    #        - '%kernel.project_dir%/var/sessions' 
