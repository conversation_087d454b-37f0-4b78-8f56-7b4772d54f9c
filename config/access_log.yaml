parameters:
    access_log:
        entities:
            App\Entity\Widget:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []
                
            App\Entity\WidgetData:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\WidgetFeature:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\WidgetFeatureAttribute:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\Site:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\ReferenceTranslation:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\LocaleTranslation:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\TranslationKey:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\FeatureSetting:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\Feature:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\LocalKeyJsonRelease:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\ProfileMenu:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\RoleMenu:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\SettingsModel:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\MediaDirectory:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []

            App\Entity\Media:
                enabled: true
                excluded_fields: []
                excluded_routes: []
                excluded_commands: []