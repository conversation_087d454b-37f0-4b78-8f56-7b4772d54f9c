<?php

namespace App\Form;

use App\Entity\Channel;
use App\Entity\Language;
use App\Entity\StaticPage;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class StaticPageFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('page_title', TextType::class, [
                'attr' => [
                    'class' => 'form-control form-control-lg',
                    'placeholder' => 'Enter page title'
                ],
                'label' => 'Page Title',
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'Page title is required']),
                    new Length(['max' => 255])
                ]
            ])
            ->add('language', EntityType::class, [
                'class' => Language::class,
                'choices' => $options['languages'],
                'choice_label' => 'label',
                'attr' => ['class' => 'form-select'],
                'label' => 'Language',
                'required' => false,
                'placeholder' => 'Choose a language',
            ])
            ->add('channel', EntityType::class, [
                'class' => Channel::class,
                'choice_label' => 'name',
                'attr' => ['class' => 'form-select'],
                'label' => 'Channel',
                'placeholder' => 'Choose a channel',
                'constraints' => [
                    new NotBlank(['message' => 'Please select a channel'])
                ]
            ])
            ->add('content', TextareaType::class, [
                'attr' => [
                    'id' => 'editor',
                    'class' => 'form-control',
                    'rows' => 10
                ],
                'label' => 'Content',
                'data'=>$options['content'],
                'mapped' => false, 
                'required' => false,
                'constraints' => [
                    new Length(['max' => 1000000000])
                ]
                ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => StaticPage::class,
            'brand' => null,
            'country' => null,
            'languages' => [],
            'content'=>''
        ]);

        $resolver->setAllowedTypes('languages', 'array');
    }
}