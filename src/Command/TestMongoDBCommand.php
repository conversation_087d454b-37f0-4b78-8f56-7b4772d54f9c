<?php

namespace App\Command;

use App\Service\MongoDBService;
use App\Service\MongoDBQueryService;
use App\Service\VehicleModelManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-mongodb',
    description: 'Test MongoDB connection and services',
)]
class TestMongoDBCommand extends Command
{
    public function __construct(
        private MongoDBService $mongoDBService,
        private MongoDBQueryService $mongoDBQueryService,
        private VehicleModelManager $vehicleModelManager
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Testing MongoDB Connection and Services');

        try {
            // Test 1: Basic MongoDB Service
            $io->section('1. Testing MongoDBService');
            
            $database = $this->mongoDBService->getDatabase();
            $io->success('✅ MongoDB connection established');
            $io->text('Database name: ' . $database->getDatabaseName());

            // Test 2: Insert and retrieve a document
            $io->section('2. Testing CRUD Operations');
            
            $testCollection = 'test_symfony_collection';
            $testDoc = [
                'test_field' => 'symfony_test',
                'timestamp' => new \DateTime(),
                'data' => ['framework' => 'Symfony', 'database' => 'MongoDB']
            ];

            $insertResult = $this->mongoDBService->insertOne($testCollection, $testDoc);
            if ($insertResult['success']) {
                $io->success('✅ Document inserted with ID: ' . $insertResult['insertedId']);
                
                // Find the document
                $findResult = $this->mongoDBService->findOne($testCollection, ['_id' => new \MongoDB\BSON\ObjectId($insertResult['insertedId'])]);
                if ($findResult['success'] && $findResult['document']) {
                    $io->success('✅ Document retrieved successfully');
                    $io->text('Document data: ' . json_encode($findResult['document'], JSON_PRETTY_PRINT));
                    
                    // Clean up
                    $deleteResult = $this->mongoDBService->deleteOne($testCollection, ['_id' => new \MongoDB\BSON\ObjectId($insertResult['insertedId'])]);
                    if ($deleteResult['success']) {
                        $io->success('✅ Test document cleaned up');
                    }
                } else {
                    $io->error('❌ Failed to retrieve document');
                }
            } else {
                $io->error('❌ Failed to insert document: ' . $insertResult['error']);
            }

            // Test 3: MongoDBQueryService
            $io->section('3. Testing MongoDBQueryService');
            
            $queryTestDoc = [
                'service_test' => 'query_service',
                'timestamp' => new \DateTime(),
                'test_data' => ['type' => 'query_test']
            ];

            $queryInsertResult = $this->mongoDBQueryService->insertOne($testCollection, $queryTestDoc);
            if ($queryInsertResult->getCode() === 200) {
                $io->success('✅ MongoDBQueryService insert successful');
                
                $responseData = json_decode($queryInsertResult->getData(), true);
                $insertedId = $responseData['insertedId'];
                
                // Test find
                $queryFindResult = $this->mongoDBQueryService->find($testCollection, ['service_test' => 'query_service']);
                if ($queryFindResult->getCode() === 200) {
                    $io->success('✅ MongoDBQueryService find successful');
                    
                    $findData = json_decode($queryFindResult->getData(), true);
                    $io->text('Found ' . count($findData['documents']) . ' document(s)');
                    
                    // Clean up
                    $queryDeleteResult = $this->mongoDBQueryService->deleteOne($testCollection, ['_id' => ['$oid' => $insertedId]]);
                    if ($queryDeleteResult->getCode() === 200) {
                        $io->success('✅ MongoDBQueryService delete successful');
                    }
                } else {
                    $io->error('❌ MongoDBQueryService find failed');
                }
            } else {
                $io->error('❌ MongoDBQueryService insert failed');
            }

            // Test 4: VehicleModelManager (if possible)
            $io->section('4. Testing VehicleModelManager');
            
            try {
                $brands = ['BMW', 'AUDI'];
                $vehicleModels = $this->vehicleModelManager->getVehicleModelWithMongoDB($brands);
                
                if ($vehicleModels['success']) {
                    $io->success('✅ VehicleModelManager query successful');
                    $io->text('Found ' . $vehicleModels['count'] . ' vehicle models for brands: ' . implode(', ', $brands));
                } else {
                    $io->warning('⚠️ VehicleModelManager query returned no results (this is normal if no data exists)');
                }
            } catch (\Exception $e) {
                $io->warning('⚠️ VehicleModelManager test skipped: ' . $e->getMessage());
            }

            // Test 5: List collections
            $io->section('5. Database Information');
            
            $collections = iterator_to_array($database->listCollections());
            $io->text('Available collections:');
            foreach ($collections as $collection) {
                $io->text('  - ' . $collection->getName());
            }

            $io->success('🎉 All MongoDB tests completed successfully!');
            
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('❌ MongoDB test failed: ' . $e->getMessage());
            $io->text('Stack trace: ' . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }
}
