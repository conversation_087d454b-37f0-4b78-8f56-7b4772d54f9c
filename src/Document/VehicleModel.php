<?php

namespace App\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as ODM;

#[ODM\Document(collection: 'vehicleLabel')]
class VehicleModel
{
    #[ODM\Id]
    private ?string $id = null;

    #[ODM\Field(type: 'string')]
    private ?string $label = null;

    #[ODM\Field(type: 'string')]
    private ?string $brand = null;

    #[ODM\Field(type: 'collection')]
    private array $lcdv = [];

    #[ODM\Field(type: 'bool')]
    private bool $isO2x = false;

    #[ODM\Field(type: 'string')]
    private ?string $sdp = null;

    #[ODM\Field(type: 'string')]
    private ?string $defaultImage = null;

    #[ODM\Field(type: 'date')]
    private ?\DateTime $creationAt = null;

    #[ODM\Field(type: 'date')]
    private ?\DateTime $updateAt = null;

    public function __construct()
    {
        $this->creationAt = new \DateTime();
        $this->updateAt = new \DateTime();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getBrand(): string
    {
        return $this->brand;
    }

    public function setBrand(string $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    public function getDefaultImage(): string
    {
        return $this->defaultImage;
    }

    public function setDefaultImage(string $defaultImage): self
    {
        $this->defaultImage = $defaultImage;

        return $this;
    }

    public function getLcdv(): array
    {
        return $this->lcdv;
    }

    public function setLcdv(array $lcdv): self
    {
        $this->lcdv = $lcdv;

        return $this;
    }

    public function getIsO2x(): ?bool
    {
        return $this->isO2x;
    }

    public function setIsO2x(bool $isO2x): self
    {
        $this->isO2x = $isO2x;

        return $this;
    }

    public function getSdp(): ?string
    {
        return $this->sdp;
    }

    public function setSdp(string $sdp): self
    {
        $this->sdp = $sdp;

        return $this;
    }

    public function getCreationAt(): ?\DateTime
    {
        return $this->creationAt;
    }

    public function setCreationAt(\DateTime $creationAt): self
    {
        $this->creationAt = $creationAt;

        return $this;
    }

    public function getUpdateAt(): ?\DateTime
    {
        return $this->updateAt;
    }

    public function setUpdateAt(\DateTime $updateAt): self
    {
        $this->updateAt = $updateAt;

        return $this;
    }
}