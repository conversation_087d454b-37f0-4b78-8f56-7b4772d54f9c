<?php


namespace App\Controller;

use App\Entity\AccessLog;
use App\Repository\AccessLogRepository;
use App\Security\User;
use App\Service\AccessLogManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;


#[Route(path: '/access-logs', name: 'access_log_')]
class  extends AbstractController
{

    #[Route(path: '/', name: 'index')]
    public function index(#[CurrentUser] User $user)
    {
        return $this->render('access_logs/index.html.twig', [
            'logs' => [],
            'profile' => $user->getProfile()
        ]);
    }

    #[Route(path: '/paginate', name: 'paginate', methods:["GET","POST"])]
    public function paginateAction(Request $request, AccessLogManager $manager): Response
    {
        $length = $request->get('length', 0);
       

        $start = $request->get('start', 0);
        
        if($length){
            $length =  ($length > -1) ? $length : 0;
            if($start && ($start > -1)){
                $start = $start / $length;
            }
        }
        $orders = $request->get('order');
        $columns = $request->get('columns');
        $search = (string) $request->get('search')['value'];
        $output = $manager->getAccessLogsData($orders, $columns, $search, $start, $length);
        
        return new Response(json_encode($output), 200, ['Content-Type' => 'application/json']);
    }

    #[Route(path: '/{log}', name: 'show', methods:["GET"])]
    public function show(AccessLog $log)
    {
        return $this->render('access_logs/show.html.twig', [
            'log' => $log
        ]);
    }
}
