<?php


namespace App\Controller;

use App\Entity\AccessLog;
use App\Form\DebugApiFormType;
use App\Repository\AccessLogRepository;
use App\Security\User;
use App\Service\AccessLogManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;


#[Route(path: '/debug_api', name: 'debug_api_')]
class DebugApiController extends AbstractController
{

    #[Route(path: '/', name: 'form')]
    public function index(#[CurrentUser] User $user)
    {
        $form = $this->createForm(DebugApiFormType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

        return $this->render('access_logs/index.html.twig', [
            'logs' => [],
            'profile' => $user->getProfile()
        ]);
    }

    #[Route(path: '/paginate', name: 'paginate', methods:["GET","POST"])]
    public function paginateAction(Request $request, AccessLogManager $manager): Response
    {
        $length = $request->get('length', 0);
       

        $start = $request->get('start', 0);
        
        if($length){
            $length =  ($length > -1) ? $length : 0;
            if($start && ($start > -1)){
                $start = $start / $length;
            }
        }
        $orders = $request->get('order');
        $columns = $request->get('columns');
        $search = (string) $request->get('search')['value'];
        $output = $manager->getAccessLogsData($orders, $columns, $search, $start, $length);
        
        return new Response(json_encode($output), 200, ['Content-Type' => 'application/json']);
    }

    #[Route(path: '/{log}', name: 'show', methods:["GET"])]
    public function show(AccessLog $log)
    {
        return $this->render('access_logs/show.html.twig', [
            'log' => $log
        ]);
    }
}
