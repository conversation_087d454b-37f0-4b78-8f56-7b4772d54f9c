<?php


namespace App\Controller;

use App\Entity\AccessLog;
use App\Form\DebugApiFormType;
use App\Repository\AccessLogRepository;
use App\Security\User;
use App\Service\AccessLogManager;
use App\Service\MongoDBService;
use App\Service\MongoDBQueryService;
use App\Service\VehicleModelManager;
use MongoDB\BSON\ObjectId;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;


#[Route(path: '/debug_api', name: 'debug_api_')]
class DebugApiController extends AbstractController
{
    public function __construct(
        private MongoDBService $mongoDBService,
        private MongoDBQueryService $mongoDBQueryService,
        private VehicleModelManager $vehicleModelManager
    ) {}

    #[Route(path: '/', name: 'form')]
    public function index(#[CurrentUser] User $user, Request $request): Response
    {
        $form = $this->createForm(DebugApiFormType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();

            try {
                // Test MongoDB connection and operations
                $testResult = $this->testMongoDBConnection($data);

                if ($testResult['success']) {
                    $this->addFlash('success', 'MongoDB operation completed successfully! ' . $testResult['message']);
                } else {
                    $this->addFlash('error', 'MongoDB operation failed: ' . $testResult['error']);
                }
            } catch (\Exception $e) {
                $this->addFlash('error', 'An error occurred: ' . $e->getMessage());
            }
        }

        return $this->render('debug_api/index.html.twig', [
            'form' => $form,
        ]);
    }

    #[Route(path: '/test-connection', name: 'test_connection')]
    public function testConnection(): Response
    {
        try {
            // Test basic MongoDB connection
            $database = $this->mongoDBService->getDatabase();
            $collections = iterator_to_array($database->listCollections());

            $connectionInfo = [
                'status' => 'Connected',
                'database' => $database->getDatabaseName(),
                'collections' => array_map(fn($col) => $col->getName(), $collections),
                'collection_count' => count($collections)
            ];

            return $this->json([
                'success' => true,
                'connection' => $connectionInfo
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function testMongoDBConnection(array $formData): array
    {
        try {
            // Create a test document based on form data
            $testDocument = [
                'form_data' => $formData,
                'timestamp' => new \DateTime(),
                'test_type' => 'debug_api_form_submission',
                'user' => $this->getUser()?->getUserIdentifier() ?? 'anonymous'
            ];

            // Test collection name
            $testCollection = 'debug_api_submissions';

            // Insert the test document
            $result = $this->mongoDBService->insertOne($testCollection, $testDocument);

            if ($result['success']) {
                // Try to find the document we just inserted
                $findResult = $this->mongoDBService->findOne($testCollection, ['_id' => new ObjectId($result['insertedId'])]);

                if ($findResult['success'] && $findResult['document']) {
                    return [
                        'success' => true,
                        'message' => "Document inserted with ID: {$result['insertedId']} and successfully retrieved."
                    ];
                } else {
                    return [
                        'success' => false,
                        'error' => 'Document was inserted but could not be retrieved.'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'error' => $result['error']
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

}
