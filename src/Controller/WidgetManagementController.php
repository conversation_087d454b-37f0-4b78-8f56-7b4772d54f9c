<?php

namespace App\Controller;

use App\Entity\WidgetData;
use App\Repository\MediaRepository;
use App\Service\WidgetManager;
use App\Entity\Widget;
use App\Repository\BrandRepository;
use App\Repository\CountryRepository;
use App\Security\User;
use App\Repository\SiteRepository;
use App\Repository\WidgetDataRepository;
use App\Repository\WidgetRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Component\Form\Form;
use Webmozart\Assert\Assert;

#[Route('/widget_management', name: 'widget_management_')]
class WidgetManagementController extends AbstractController
{
    #[Route('/', name: 'list')]
    public function manageWidgets(WidgetManager $widgetManager, #[CurrentUser] User $user): Response
    {
        $profile = $user->getProfile();
        $brand = $profile->getSite()->getBrand();
        $country = $profile->getSite()->getCountry();

        return $this->render('widget/data_list.html.twig', [
            'widgets' => $widgetManager->getWidgetAndConfig($brand, $country),
        ]);
    }

    #[Route('/download/{id}', name: 'download', methods: ['GET'])]
    public function download(Widget $widget): Response
    {
        $response = new Response(json_encode($widget->getFeaturesConfiguration()));
        // Set the appropriate headers for file download
        $response->headers->set('Content-Type', 'text/plain');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $widget->getName() . '.json"');
        return $response;
    }

    #[Route('/load/{id}', name: 'load')]
    public function load(
        Request $request,
        Widget $widget,
        WidgetManager $widgetManager,
        #[CurrentUser] User $user,
        SiteRepository $siteRepository,
        MediaRepository $mediaRepository,
        WidgetDataRepository $widgetDataRepository,
        string $currentSource = '',
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $site = $siteRepository->find($site->getId());
        $brand = $site->getBrand();
        $country = $site->getCountry();
        $languages = $site->getLanguages();
        $referenceLanguage = $site->getPreferedLanguage();
        $brandAdmin = $profile->isBrandAdministrator();
        $isWidgetEnabled = $labels = [];
        $forms = $formViews = [];
        $sourceRequest = $request->get('source', null);
        $widgetForms = $widgetManager->load($widget, $brand, $country, $languages, $request->getLocale(), $brandAdmin);
        $language = count($site->getLanguages()) > 0 ? $site->getLanguages()->toArray() : [$site->getPreferedLanguage()];
        foreach ($widgetForms as $source => $featureForms) {
            $isWidgetEnabled[$source] = $widgetManager->isWidgetEnabled($widget, $brand, $country, $source);
            $labels[$source] = $widgetManager->getLabels($widget, $brand, $country, $source,  isset($language[0]) ? $language[0] : null, $referenceLanguage);
            foreach ($featureForms as $feature => $form) {
                $formViews[$source][$feature]['form'] = $form['form']->createView();
                $formViews[$source][$feature]['title'] = $form['title'];
                $forms[$source][$feature] = $form['form'];
            }
        }

        if ($request->getMethod() == 'POST') {
            $form = $this->getSubmittedForm($request, $forms);
            if ($form->isSubmitted() && $form->isValid()) {
                $featureCode = $form->getData()['code'];
                $widgetFeature = $widgetManager->saveFeature($widget, $featureCode, $form->getData(), $brand, $country, $languages);

                // Handle AJAX requests (for Save All Features functionality)
                if ($request->isXmlHttpRequest() || $request->request->get('combined')) {
                    if ($widgetFeature->getId() !== null) {
                        $this->addFlash('success', 'save_widget_feature_success');
                        return new JsonResponse([
                            'success' => true,
                            'message' => 'Feature saved successfully',
                            'feature' => $featureCode
                        ]);
                    } else {
                        $this->addFlash('danger', 'save_widget_feature_error');
                        return new JsonResponse([
                            'success' => false,
                            'message' => 'Failed to save feature',
                            'feature' => $featureCode
                        ], 400);
                    }
                }

            } else {
                // Handle AJAX validation errors
                if ($request->isXmlHttpRequest() || $request->request->get('combined')) {
                    $errors = [];
                    foreach ($form->getErrors(true) as $error) {
                        $errors[] = $error->getMessage();
                    }
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Validation failed',
                        'errors' => $errors
                    ], 400);
                }

                $this->updateCurrentSubmittedForm($request, $formViews, $forms, $form);
            }
        }
        //we should load medias only if we have a declared feature[field] with media type
        $media = $mediaRepository->findBy(['site' => $site]);
        $widgetData = $widgetDataRepository->findBy(['widget' => $widget, 'brand' => $brand, 'country' => $country]);

        $s3Resources = $widgetManager->getWidgetS3ResourcesStatus($widget, $brand, $country, $languages, $referenceLanguage);
        return $this->render('widget/widget.html.twig', [
            'forms' => $formViews,
            'medias' => $media,
            'widget' => $widget,
            'currentSource' => $sourceRequest ?? $currentSource,
            'isWidgetEnabled' => $isWidgetEnabled,
            'labels' => $labels,
            'isMultilanguageSite' => $languages->count() > 1,
            's3Resources' => $s3Resources,
            'widgetData' => $widgetData,
            'accessLogs' => [],
            'profile' => $profile,
            'brand' => $brand,
            'country' => $country
        ]);
    }

    /**
     * @param Form[] $forms
     */
    private function getSubmittedForm(Request $request, array $forms): ?Form
    {
        $formkeys = $request->request->keys();
        $submittedFormName = reset($formkeys);
        foreach ($forms as $source => $featureForms) {
            foreach ($featureForms as $code => $form) {
                if ($form->getName() == $submittedFormName) {
                    $form->handleRequest();
                    return $form;
                }
            }
        }
        return null;
    }

    #[Route('/enable/{id}', name: 'enable', methods: ['POST'])]
    public function enable(SiteRepository $siteRepository, Widget $widget, Request $request, WidgetManager $widgetManager, #[CurrentUser] User $user): JsonResponse
    {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $site = $siteRepository->find($site->getId());
        $brand = $site?->getBrand();
        $country = $site->getCountry();
        $source = $request->request->get('source');
        $enabled = ($request->request->get('enabled') == "true") ? true : false;

        $widgetData = ($brand && $source) ? $widgetManager->enable($widget, $brand, $country, $source, $enabled) : null;

        return new JsonResponse($widgetData?->isEnabled());
    }

    #[Route('/publish/{id}', name: 'publish')]
    public function publishWidget(WidgetManager $widgetManager, WidgetData $widgetData, #[CurrentUser] User $user, SiteRepository $siteRepository): Response
    {
        $profile = $user->getProfile();
        $site = $siteRepository->find($profile->getSite()->getId());
        $languages = $site->getLanguages();
        $referenceLanguage = $site->getPreferedLanguage();
        $response = $widgetManager->publishWidgetData($widgetData, $languages, $referenceLanguage);
        if ($response === true) {
            $this->addFlash('success', 'widget published successfully');
        } else {
            $this->addFlash('danger', 'failed to publish widget');
        }

        return $this->redirectToRoute('widget_management_list');
    }

    #[Route('/s3-resource-content', name: 's3_content')]
    public function s3ResourceContent(Request $request, WidgetManager $widgetManager): JsonResponse
    {
        $url = $request->query->get('url');
        Assert::stringNotEmpty($url);
        $content = $widgetManager->getWidgetS3ResourceContent($url);
        $content = json_decode($content, true);
        $content = $content ? $content : ['error' => 'Failed to load s3 resource content'];
        return new JsonResponse($content);
    }

    /**
     * @param Request $request
     * @param array $formViews
     * @param array $forms
     * @param Form $currentForm
     *
     * @return void
     */
    private function updateCurrentSubmittedForm(Request $request, array &$formViews, array $forms, Form $currentForm): void
    {
        $formkeys = $request->request->keys();
        $submittedFormName = reset($formkeys);
        foreach ($forms as $source => $featureForms) {
            foreach ($featureForms as $code => $form) {
                if ($form->getName() == $submittedFormName) {
                    $formViews[$source][$code]['form'] = $currentForm->createView();
                }
            }
        }
    }

    #[Route(path: '/paginate', name: 'paginate')]
    public function paginateAction(Request $request, WidgetManager $manager, BrandRepository $brandRepository, CountryRepository $countryRepository, WidgetRepository $widgetRepository): Response
    {
        try {
            $widget = $widgetRepository->findOneBy(['id' => $request->get('widget')]);
            $brand = $brandRepository->findOneBy(['id' => $request->get('brand')]);
            $country = $countryRepository->findOneBy(['id' => $request->get('country')]);
            $output = $manager->getAccessLog($widget, $brand, $country);
            return new Response(json_encode($output), 200, ['Content-Type' => 'application/json']);
        } catch (\Throwable $e) {
            $output = array(
                'data' => [],
                'recordsFiltered' => 0,
                'recordsTotal' => 0
            );
            return new Response(json_encode($output), 200, ['Content-Type' => 'application/json']);
        }
    }
}
