<?php

namespace App\Service;

use App\Helpers\WSResponse;
use App\Helpers\LoggerTrait;
use MongoDB\BSON\ObjectId;
use MongoDB\Exception\Exception as MongoException;
use Symfony\Component\HttpFoundation\Response;

/**
 * Enhanced MongoDB Query Service that provides the same interface as MongoAtlasQueryService
 * but uses direct MongoDB driver instead of HTTP API calls
 */
class MongoDBQueryService
{
    use LoggerTrait;

    public function __construct(
        private MongoDBService $mongoDBService,
        private string $dataSource,
        private string $database
    ) {}

    /**
     * Find documents in collection
     */
    public function find(string $collection, ?array $filter): WSResponse
    {
        try {
            $result = $this->mongoDBService->find($collection, $filter ?? []);
            
            if ($result['success']) {
                $responseData = [
                    'documents' => $result['documents']
                ];
                return new WSResponse(Response::HTTP_OK, json_encode($responseData));
            } else {
                return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $result['error']);
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBQueryService::find error: ' . $e->getMessage());
            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    /**
     * Find one document in collection
     */
    public function findOne(string $collection, ?array $filter): WSResponse
    {
        try {
            $result = $this->mongoDBService->findOne($collection, $filter ?? []);
            
            if ($result['success']) {
                $responseData = [
                    'document' => $result['document']
                ];
                return new WSResponse(Response::HTTP_OK, json_encode($responseData));
            } else {
                return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $result['error']);
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBQueryService::findOne error: ' . $e->getMessage());
            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    /**
     * Insert one document
     */
    public function insertOne(string $collection, array $document): WSResponse
    {
        try {
            $result = $this->mongoDBService->insertOne($collection, $document);
            
            if ($result['success']) {
                $responseData = [
                    'insertedId' => $result['insertedId']
                ];
                return new WSResponse(Response::HTTP_OK, json_encode($responseData));
            } else {
                return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $result['error']);
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBQueryService::insertOne error: ' . $e->getMessage());
            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    /**
     * Insert multiple documents
     */
    public function insertMany(string $collection, array $documents): WSResponse
    {
        try {
            $result = $this->mongoDBService->insertMany($collection, $documents);
            
            if ($result['success']) {
                $responseData = [
                    'insertedIds' => $result['insertedIds'],
                    'insertedCount' => $result['insertedCount']
                ];
                return new WSResponse(Response::HTTP_OK, json_encode($responseData));
            } else {
                return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $result['error']);
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBQueryService::insertMany error: ' . $e->getMessage());
            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    /**
     * Update one document
     */
    public function updateOne(string $collection, array $filter, array $update): WSResponse
    {
        try {
            // Convert string IDs to ObjectId if needed
            if (isset($filter['_id']) && is_array($filter['_id']) && isset($filter['_id']['$oid'])) {
                $filter['_id'] = new ObjectId($filter['_id']['$oid']);
            }

            // Ensure update has proper MongoDB update operators
            if (!isset($update['$set']) && !isset($update['$unset']) && !isset($update['$inc'])) {
                $update = ['$set' => $update];
            }

            $result = $this->mongoDBService->updateOne($collection, $filter, $update);
            
            if ($result['success']) {
                $responseData = [
                    'matchedCount' => $result['matchedCount'],
                    'modifiedCount' => $result['modifiedCount']
                ];
                return new WSResponse(Response::HTTP_OK, json_encode($responseData));
            } else {
                return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $result['error']);
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBQueryService::updateOne error: ' . $e->getMessage());
            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    /**
     * Update multiple documents
     */
    public function updateMany(string $collection, array $filter, array $update): WSResponse
    {
        try {
            // Ensure update has proper MongoDB update operators
            if (!isset($update['$set']) && !isset($update['$unset']) && !isset($update['$inc'])) {
                $update = ['$set' => $update];
            }

            $result = $this->mongoDBService->updateMany($collection, $filter, $update);
            
            if ($result['success']) {
                $responseData = [
                    'matchedCount' => $result['matchedCount'],
                    'modifiedCount' => $result['modifiedCount']
                ];
                return new WSResponse(Response::HTTP_OK, json_encode($responseData));
            } else {
                return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $result['error']);
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBQueryService::updateMany error: ' . $e->getMessage());
            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    /**
     * Delete one document
     */
    public function deleteOne(string $collection, array $filter): WSResponse
    {
        try {
            // Convert string IDs to ObjectId if needed
            if (isset($filter['_id']) && is_array($filter['_id']) && isset($filter['_id']['$oid'])) {
                $filter['_id'] = new ObjectId($filter['_id']['$oid']);
            }

            $result = $this->mongoDBService->deleteOne($collection, $filter);
            
            if ($result['success']) {
                $responseData = [
                    'deletedCount' => $result['deletedCount']
                ];
                return new WSResponse(Response::HTTP_OK, json_encode($responseData));
            } else {
                return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $result['error']);
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBQueryService::deleteOne error: ' . $e->getMessage());
            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    /**
     * Delete multiple documents
     */
    public function deleteMany(string $collection, array $filter): WSResponse
    {
        try {
            $result = $this->mongoDBService->deleteMany($collection, $filter);
            
            if ($result['success']) {
                $responseData = [
                    'deletedCount' => $result['deletedCount']
                ];
                return new WSResponse(Response::HTTP_OK, json_encode($responseData));
            } else {
                return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $result['error']);
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBQueryService::deleteMany error: ' . $e->getMessage());
            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    /**
     * Aggregate documents
     */
    public function aggregate(string $collection, array $pipeline): WSResponse
    {
        try {
            $result = $this->mongoDBService->aggregate($collection, $pipeline);
            
            if ($result['success']) {
                $responseData = [
                    'documents' => $result['documents']
                ];
                return new WSResponse(Response::HTTP_OK, json_encode($responseData));
            } else {
                return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $result['error']);
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBQueryService::aggregate error: ' . $e->getMessage());
            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    /**
     * Find documents with duplicate keys (compatible with existing method)
     */
    public function findDuplicateKeys(string $collectionName, array $keys): array
    {
        try {
            $filter = [
                'lcdv' => [
                    '$elemMatch' => [
                        '$in' => $keys
                    ]
                ]
            ];

            $result = $this->mongoDBService->find($collectionName, $filter);
            
            if ($result['success']) {
                return $result['documents'];
            } else {
                $this->logger->error('MongoDBQueryService::findDuplicateKeys error: ' . $result['error']);
                return [];
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBQueryService::findDuplicateKeys error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Alias for deleteOne to maintain compatibility
     */
    public function delete(string $collection, array $filter): WSResponse
    {
        return $this->deleteOne($collection, $filter);
    }

    /**
     * Find or create document (upsert operation)
     */
    public function findOrCreate(string $collection, array $filter, array $document): WSResponse
    {
        try {
            // First try to find the document
            $findResult = $this->mongoDBService->findOne($collection, $filter);
            
            if ($findResult['success'] && $findResult['document']) {
                // Document exists, update it
                $update = ['$set' => $document];
                return $this->updateOne($collection, $filter, $update);
            } else {
                // Document doesn't exist, insert it
                return $this->insertOne($collection, $document);
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBQueryService::findOrCreate error: ' . $e->getMessage());
            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }
}
