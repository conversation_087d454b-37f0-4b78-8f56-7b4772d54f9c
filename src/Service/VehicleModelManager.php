<?php

namespace App\Service;

use App\Document\VehicleModel;
use App\Repository\BrandRepository;
use App\Service\MongoAtlasQueryService;
use App\Service\MongoDBService;
use MongoDB\BSON\ObjectId;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

class VehicleModelManager
{
    const COLLECTION = "vehicleLabel";
    public function __construct(
        private BrandRepository $brandRepository,
        private MongoAtlasQueryService $mongoAtlasQueryService,
        private MongoDBService $mongoDBService,
        private SerializerInterface $serializerInterface,
        private NormalizerInterface $normalizer
    ) {}

    public function save(VehicleModel $vehicleModel)
    {
        $vehicleModelArr = $this->normalizer->normalize($vehicleModel);
        $this->mongoAtlasQueryService->insertOne(self::COLLECTION, $vehicleModelArr);
    }

    public function update(VehicleModel $vehicleModel, string $id)
    {
        $vehicleModelArr = $this->normalizer->normalize($vehicleModel);
        $this->mongoAtlasQueryService->updateOne(self::COLLECTION, ['_id' => ['$oid' => $id]],$vehicleModelArr);
    }

    // New methods using direct MongoDB connection
    public function saveWithMongoDB(VehicleModel $vehicleModel): array
    {
        $vehicleModelArr = $this->normalizer->normalize($vehicleModel);
        return $this->mongoDBService->insertOne(self::COLLECTION, $vehicleModelArr);
    }

    public function updateWithMongoDB(VehicleModel $vehicleModel, string $id): array
    {
        $vehicleModelArr = $this->normalizer->normalize($vehicleModel);
        $filter = ['_id' => new ObjectId($id)];
        $update = ['$set' => $vehicleModelArr];
        return $this->mongoDBService->updateOne(self::COLLECTION, $filter, $update);
    }

    public function findWithMongoDB(array $filter = []): array
    {
        return $this->mongoDBService->find(self::COLLECTION, $filter);
    }

    public function findOneWithMongoDB(string $id): array
    {
        $filter = ['_id' => new ObjectId($id)];
        return $this->mongoDBService->findOne(self::COLLECTION, $filter);
    }

    public function deleteWithMongoDB(string $id): array
    {
        $filter = ['_id' => new ObjectId($id)];
        return $this->mongoDBService->deleteOne(self::COLLECTION, $filter);
    }

    public function findDuplicateKeysWithMongoDB(array $lcdv): array
    {
        $filter = [
            'lcdv' => [
                '$elemMatch' => [
                    '$in' => $lcdv
                ]
            ]
        ];
        return $this->mongoDBService->find(self::COLLECTION, $filter);
    }

    public function getVehicleModelWithMongoDB(array $brands): array
    {
        $filter = ['brand' => ['$in' => $brands]];
        return $this->mongoDBService->find(self::COLLECTION, $filter);
    }

    public function findDuplicateKeys(array $lcdv): array
    {
        $duplicate = $this->mongoAtlasQueryService->findDuplicateKeys(self::COLLECTION, $lcdv);
        return $duplicate;
    }

    public function getVehicleModel(array $brand): array
    {
        $filter = ['brand' => ['$in' => $brand]];
        $response = $this->mongoAtlasQueryService->find(self::COLLECTION, $filter);
        if ($response->getCode() == 200) {
            return json_decode($response->getData(), true)['documents'] ?? [];
        }
        return [];
    }

    public function findModel(string $id): VehicleModel
    {
        $response = $this->mongoAtlasQueryService->find(self::COLLECTION, ['_id' => ['$oid' => $id]]);
        $data = json_decode($response->getData(), true);
        $documentArray = $data['documents'][0];
        $vehicleModel = $this->serializerInterface->deserialize(
            json_encode($documentArray),
            VehicleModel::class,
            'json'
        );
        return $vehicleModel;
    }

    public function removeModel(string $id)
    {
        $response = $this->mongoAtlasQueryService->delete(self::COLLECTION,['_id' => ['$oid' => $id]]);
        if ($response->getCode() == 200) {
            return true;
        }
        return false;
    }
}
