<?php

namespace App\Repository;

use App\Entity\Widget;
use App\Entity\WidgetData;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<WidgetData>
 *
 * @method WidgetData|null find($id, $lockMode = null, $lockVersion = null)
 * @method WidgetData|null findOneBy(array $criteria, array $orderBy = null)
 * @method WidgetData[]    findAll()
 * @method WidgetData[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class WidgetDataRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, WidgetData::class);
    }

    public function save(WidgetData $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(WidgetData $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
