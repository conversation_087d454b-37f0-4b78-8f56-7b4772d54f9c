<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250124103431 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
{
    // Assign Menus IDs to variables
    $this->addSql("SET @GLOBAL_FEATURES_MENU_ID = (SELECT id FROM `menu` WHERE label = 'global_features')");
    $this->addSql("SET @BRAND_FEATURES_MENU_ID = (SELECT id FROM `menu` WHERE label = 'brand_features')");
    $this->addSql("SET @LOCAL_FEATURES_MENU_ID = (SELECT id FROM `menu` WHERE label = 'local_features')");

    // Delete existing role_menu entries for the specific menus
    $this->addSql("DELETE FROM role_menu WHERE menu_id = @GLOBAL_FEATURES_MENU_ID");
    $this->addSql("DELETE FROM role_menu WHERE menu_id = @BRAND_FEATURES_MENU_ID");
    $this->addSql("DELETE FROM role_menu WHERE menu_id = @LOCAL_FEATURES_MENU_ID");

    // Assign role IDs to variables
    $this->addSql("SET @LOCAL_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Local Technical Administrator')");
    $this->addSql("SET @SUPER_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Super Administrator')");
    $this->addSql("SET @TECHNICAL_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Technical Administrator')");
    $this->addSql("SET @FUNCTIONAL_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Functional Administrator')");
    $this->addSql("SET @READER_ID = (SELECT id FROM `role` WHERE label = 'Reader')");
    $this->addSql("SET @OPERATIONS_ID = (SELECT id FROM `role` WHERE label = 'Operations')");
    $this->addSql("SET @WEBMASTER_ID = (SELECT id FROM `role` WHERE label = 'Webmaster')");

    // Insert new role_menu entries
    $this->addSql("
        INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`) 
        SELECT NULL, @SUPER_ADMIN_ID, @GLOBAL_FEATURES_MENU_ID, 'W' UNION ALL
        SELECT NULL, @TECHNICAL_ADMIN_ID, @BRAND_FEATURES_MENU_ID, 'W' UNION ALL
        SELECT NULL, @FUNCTIONAL_ADMIN_ID, @BRAND_FEATURES_MENU_ID, 'W' UNION ALL
        SELECT NULL, @LOCAL_ADMIN_ID, @LOCAL_FEATURES_MENU_ID, 'W' UNION ALL
        SELECT NULL, @READER_ID, @LOCAL_FEATURES_MENU_ID, 'R' UNION ALL
        SELECT NULL, @OPERATIONS_ID, @LOCAL_FEATURES_MENU_ID, 'R' UNION ALL
        SELECT NULL, @WEBMASTER_ID, @LOCAL_FEATURES_MENU_ID, 'R'
    ");
}

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
