<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240612164935 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

        public function up(Schema $schema): void
    {   
        $this->addSql("TRUNCATE TABLE local_key_json_release"); 
        $this->addSql("ALTER TABLE `local_key_json_release` ADD COLUMN old_filename VARCHAR(255);");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("ALTER TABLE `local_key_json_release` DROP COLUMN old_filename"); 
    }

}