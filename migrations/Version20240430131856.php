<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240430131856 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
            SELECT id INTO @MENU_ID FROM `menu` WHERE route_name = 'admin_media_index';
            DELETE FROM `role_menu` WHERE menu_id = @MENU_ID;
            DELETE FROM `menu` WHERE id = @MENU_ID;

            INSERT INTO `menu` (`parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`) VALUES
            (NULL,	'media',	NULL,	'admin_media_index',	'{\"profile\": \":profile\"}',	NULL);
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql("
            SELECT parent_id INTO @MENU_ID FROM `menu` WHERE route_name = 'admin_media_index';
            DELETE FROM `role_menu` WHERE menu_id = @MENU_ID;
            DELETE FROM `menu` WHERE route_name = 'admin_media_index';
        ");
    }
}
