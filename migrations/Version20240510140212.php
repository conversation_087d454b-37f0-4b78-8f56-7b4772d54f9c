<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240510140212 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'XX', 'Racine > XX', 1, 'XX');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'SP', 'Racine > SP', 1, 'SP');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'FT', 'Racine > FT', 1, 'FT');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'FO', 'Racine > FO', 1, 'FO');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'AH', 'Racine > AH', 1, 'AH');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'AR', 'Racine > AR', 1, 'AR');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'CY', 'Racine > CY', 1, 'CY');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'DG', 'Racine > DG', 1, 'DG');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'JE', 'Racine > JE', 1, 'JE');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'LA', 'Racine > LA', 1, 'LA');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'RM', 'Racine > RM', 1, 'RM');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'MA', 'Racine > MA', 1, 'MA');
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
