<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Exception;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250121093250 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds foreign key constraint to menu table if it does not already exist.';
    }

    public function up(Schema $schema): void
    {
        // Check if the foreign key constraint already exists
        $exists = $this->foreignKeyExists('menu', 'FK_7D053A9360E4B879');

        if (!$exists) {
            $this->addSql('ALTER TABLE menu ADD CONSTRAINT FK_7D053A9360E4B879 FOREIGN KEY (feature_id) REFERENCES feature_setting (id)');
        }
    }

    public function down(Schema $schema): void
    {
        // Check if the foreign key constraint exists before trying to drop it
        $exists = $this->foreignKeyExists('menu', 'FK_7D053A9360E4B879');

        if ($exists) {
            $this->addSql('ALTER TABLE menu DROP FOREIGN KEY FK_7D053A9360E4B879');
        }
    }

    private function foreignKeyExists(string $tableName, string $constraintName): bool
    {
        try {
            $result = $this->connection->fetchOne(
                "SELECT COUNT(*) FROM information_schema.TABLE_CONSTRAINTS 
                 WHERE TABLE_NAME = :table AND CONSTRAINT_NAME = :constraint",
                ['table' => $tableName, 'constraint' => $constraintName]
            );

            return $result > 0;
        } catch (Exception $e) {
            return false;
        }
    }
}
