<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240515074844 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE feature_parameter CHANGE value value LONGTEXT NOT NULL');
        $this->addSql('ALTER TABLE widget_feature_attribute CHANGE value value LONGTEXT NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE feature_parameter CHANGE value value VARCHAR(1000) NOT NULL');
        $this->addSql('ALTER TABLE widget_feature_attribute CHANGE value value VARCHAR(1000) NOT NULL');
    }
}
