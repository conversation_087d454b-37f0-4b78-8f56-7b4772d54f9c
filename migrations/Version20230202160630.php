<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230202160630 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'currency data';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
         $this->addSql("INSERT INTO `currency` (`code`, `name`) VALUES
            ('AFN', 'Afghanistan Afghani'),
            ('ALL', 'Albania Lek'),
            ('DZD', 'Algeria Dinar'),
            ('AOA', 'Angola Kwanza'),
            ('ARS', 'Argentina Peso'),
            ('AMD', 'Armenia Dram'),
            ('AWG', 'Aruba Guilder'),
            ('AUD', 'Australia Dollar'),
            ('AZN', 'Azerbaijan New Manat'),
            ('BSD', 'Bahamas Dollar'),
            ('BHD', 'Bahrain Dinar'),
            ('BDT', 'Bangladesh Taka'),
            ('BBD', 'Barbados Dollar'),
            ('BYN', 'Belarus Ruble'),
            ('BZD', 'Belize Dollar'),
            ('BMD', 'Bermuda Dollar'),
            ('BTN', 'Bhutan Ngultrum'),
            ('BOB', 'Bolivia Bolíviano'),
            ('BAM', 'Bosnia and Herzegovina Convertible Marka'),
            ('BWP', 'Botswana Pula'),
            ('BRL', 'Brazil Real'),
            ('BND', 'Brunei Darussalam Dollar'),
            ('BGN', 'Bulgaria Lev'),
            ('BIF', 'Burundi Franc'),
            ('KHR', 'Cambodia Riel'),
            ('CAD', 'Canada Dollar'),
            ('CVE', 'Cape Verde Escudo'),
            ('KYD', 'Cayman Islands Dollar'),
            ('CLP', 'Chile Peso'),
            ('CNY', 'China Yuan Renminbi'),
            ('COP', 'Colombia Peso'),
            ('XOF', 'Communauté Financière Africaine (BCEAO) Franc'),
            ('XAF', 'Communauté Financière Africaine (BEAC) CFA Franc BEAC'),
            ('KMF', 'Comoros Franc'),
            ('XPF', 'Comptoirs Français du Pacifique (CFP) Franc'),
            ('CDF', 'Congo/Kinshasa Franc'),
            ('CRC', 'Costa Rica Colon'),
            ('HRK', 'Croatia Kuna'),
            ('CUC', 'Cuba Convertible Peso'),
            ('CUP', 'Cuba Peso'),
            ('CZK', 'Czech Republic Koruna'),
            ('DKK', 'Denmark Krone'),
            ('DJF', 'Djibouti Franc'),
            ('DOP', 'Dominican Republic Peso'),
            ('XCD', 'East Caribbean Dollar'),
            ('EGP', 'Egypt Pound'),
            ('SVC', 'El Salvador Colon'),
            ('ERN', 'Eritrea Nakfa'),
            ('ETB', 'Ethiopia Birr'),
            ('EUR', 'Euro Member Countries'),
            ('FKP', 'Falkland Islands (Malvinas) Pound'),
            ('FJD', 'Fiji Dollar'),
            ('GMD', 'Gambia Dalasi'),
            ('GEL', 'Georgia Lari'),
            ('GHS', 'Ghana Cedi'),
            ('GIP', 'Gibraltar Pound'),
            ('GTQ', 'Guatemala Quetzal'),
            ('GGP', 'Guernsey Pound'),
            ('GNF', 'Guinea Franc'),
            ('GYD', 'Guyana Dollar'),
            ('HTG', 'Haiti Gourde'),
            ('HNL', 'Honduras Lempira'),
            ('HKD', 'Hong Kong Dollar'),
            ('HUF', 'Hungary Forint'),
            ('ISK', 'Iceland Krona'),
            ('INR', 'India Rupee'),
            ('IDR', 'Indonesia Rupiah'),
            ('XDR', 'International Monetary Fund (IMF) Special Drawing Rights'),
            ('IRR', 'Iran Rial'),
            ('IQD', 'Iraq Dinar'),
            ('IMP', 'Isle of Man Pound'),
            ('ILS', 'Israel Shekel'),
            ('JMD', 'Jamaica Dollar'),
            ('JPY', 'Japan Yen'),
            ('JEP', 'Jersey Pound'),
            ('JOD', 'Jordan Dinar'),
            ('KZT', 'Kazakhstan Tenge'),
            ('KES', 'Kenya Shilling'),
            ('KPW', 'Korea (North) Won'),
            ('KRW', 'Korea (South) Won'),
            ('KWD', 'Kuwait Dinar'),
            ('KGS', 'Kyrgyzstan Som'),
            ('LAK', 'Laos Kip'),
            ('LBP', 'Lebanon Pound'),
            ('LSL', 'Lesotho Loti'),
            ('LRD', 'Liberia Dollar'),
            ('LYD', 'Libya Dinar'),
            ('MOP', 'Macau Pataca'),
            ('MKD', 'Macedonia Denar'),
            ('MGA', 'Madagascar Ariary'),
            ('MWK', 'Malawi Kwacha'),
            ('MYR', 'Malaysia Ringgit'),
            ('MVR', 'Maldives (Maldive Islands) Rufiyaa'),
            ('MRO', 'Mauritania Ouguiya'),
            ('MUR', 'Mauritius Rupee'),
            ('MXN', 'Mexico Peso'),
            ('MDL', 'Moldova Leu'),
            ('MNT', 'Mongolia Tughrik'),
            ('MAD', 'Morocco Dirham'),
            ('MZN', 'Mozambique Metical'),
            ('MMK', 'Myanmar (Burma) Kyat'),
            ('NAD', 'Namibia Dollar'),
            ('NPR', 'Nepal Rupee'),
            ('ANG', 'Netherlands Antilles Guilder'),
            ('NZD', 'New Zealand Dollar'),
            ('NIO', 'Nicaragua Cordoba'),
            ('NGN', 'Nigeria Naira'),
            ('NOK', 'Norway Krone'),
            ('OMR', 'Oman Rial'),
            ('PKR', 'Pakistan Rupee'),
            ('PAB', 'Panama Balboa'),
            ('PGK', 'Papua New Guinea Kina'),
            ('PYG', 'Paraguay Guarani'),
            ('PEN', 'Peru Sol'),
            ('PHP', 'Philippines Peso'),
            ('PLN', 'Poland Zloty'),
            ('QAR', 'Qatar Riyal'),
            ('RON', 'Romania New Leu'),
            ('RUB', 'Russia Ruble'),
            ('RWF', 'Rwanda Franc'),
            ('SHP', 'Saint Helena Pound'),
            ('WST', 'Samoa Tala'),
            ('SAR', 'Saudi Arabia Riyal'),
            ('RSD', 'Serbia Dinar'),
            ('SCR', 'Seychelles Rupee'),
            ('SLL', 'Sierra Leone Leone'),
            ('SGD', 'Singapore Dollar'),
            ('SBD', 'Solomon Islands Dollar'),
            ('SOS', 'Somalia Shilling'),
            ('ZAR', 'South Africa Rand'),
            ('LKR', 'Sri Lanka Rupee'),
            ('SDG', 'Sudan Pound'),
            ('SRD', 'Suriname Dollar'),
            ('SZL', 'Swaziland Lilangeni'),
            ('SEK', 'Sweden Krona'),
            ('CHF', 'Switzerland Franc'),
            ('SYP', 'Syria Pound'),
            ('STD', 'São Tomé and Príncipe Dobra'),
            ('TWD', 'Taiwan New Dollar'),
            ('TJS', 'Tajikistan Somoni'),
            ('TZS', 'Tanzania Shilling'),
            ('THB', 'Thailand Baht'),
            ('TOP', 'Tonga Pa\'anga'),
            ('TTD', 'Trinidad and Tobago Dollar'),
            ('TND', 'Tunisia Dinar'),
            ('TRY', 'Turkey Lira'),
            ('TMT', 'Turkmenistan Manat'),
            ('TVD', 'Tuvalu Dollar'),
            ('UGX', 'Uganda Shilling'),
            ('UAH', 'Ukraine Hryvnia'),
            ('AED', 'United Arab Emirates Dirham'),
            ('GBP', 'United Kingdom Pound'),
            ('USD', 'United States Dollar'),
            ('UYU', 'Uruguay Peso'),
            ('UZS', 'Uzbekistan Som'),
            ('VUV', 'Vanuatu Vatu'),
            ('VEF', 'Venezuela Bolivar'),
            ('VND', 'Viet Nam Dong'),
            ('YER', 'Yemen Rial'),
            ('ZMW', 'Zambia Kwacha'),
            ('ZWD', 'Zimbabwe Dollar');");

    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
