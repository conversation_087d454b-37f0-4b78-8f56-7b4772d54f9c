<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230203142248 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'brand & country data';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
INSERT INTO `brand` (`id`, `name`, `code`) VALUES
(1, 'Citroën',  'AC'),
(2, 'Peugeot',  'AP'),
(3, 'DS',   'DS'),
(4, 'Opel', 'OP'),
(5, 'Vauxhall', 'VX'),
(6, 'Global',   'XX'),
(7, 'SPOTICAR', 'SP'),
(18,    'FIAT', 'FT'),
(19,    'FIAT PROFESSIONAL',    'FO'),
(20,    'ABARTH',   'AH'),
(21,    'ALFA ROMEO',   'AR'),
(22,    'CHRYSLER', 'CY'),
(23,    'DODGE',    'DG'),
(24,    'JEEP', 'JE'),
(25,    'LANCIA',   'LA'),
(26,    'RAM',  'RM'),
(27,    'MASERATI', 'MA');

INSERT INTO `country` (`id`, `name`, `code`) VALUES
(1, 'Afghanistan',  'AF'),
(2, 'Aland Islands',    'AX'),
(3, 'Albania',  'AL'),
(4, 'Algeria',  'DZ'),
(5, 'American Samoa',   'AS'),
(6, 'Andorra',  'AD'),
(7, 'Angola',   'AO'),
(8, 'Anguilla', 'AI'),
(9, 'Antarctica',   'AQ'),
(10,    'Antigua and Barbuda',  'AG'),
(11,    'Argentina',    'AR'),
(12,    'Armenia',  'AM'),
(13,    'Aruba',    'AW'),
(14,    'Australia',    'AU'),
(15,    'Austria',  'AT'),
(16,    'Azerbaijan',   'AZ'),
(17,    'Bahamas',  'BS'),
(18,    'Bahrain',  'BH'),
(19,    'Bangladesh',   'BD'),
(20,    'Barbados', 'BB'),
(21,    'Belarus',  'BY'),
(22,    'Belgium',  'BE'),
(23,    'Belize',   'BZ'),
(24,    'Benin',    'BJ'),
(25,    'Bermuda',  'BM'),
(26,    'Bhutan',   'BT'),
(27,    'Bolivia',  'BO'),
(28,    'Bonaire, Sint Eustatius and Saba', 'BQ'),
(29,    'Bosnia and Herzegovina',   'BA'),
(30,    'Botswana', 'BW'),
(31,    'Bouvet Island',    'BV'),
(32,    'Brazil',   'BR'),
(33,    'British Indian Ocean Territory',   'IO'),
(34,    'Brunei Darussalam',    'BN'),
(35,    'Bulgaria', 'BG'),
(36,    'Burkina Faso', 'BF'),
(37,    'Burundi',  'BI'),
(38,    'Cambodia', 'KH'),
(39,    'Cameroon', 'CM'),
(40,    'Canada',   'CA'),
(41,    'Cape Verde',   'CV'),
(42,    'Cayman Islands',   'KY'),
(43,    'Central African Republic', 'CF'),
(44,    'Chad', 'TD'),
(45,    'Chile',    'CL'),
(46,    'China',    'CN'),
(47,    'Christmas Island', 'CX'),
(48,    'Cocos (Keeling) Islands',  'CC'),
(49,    'Colombia', 'CO'),
(50,    'Comoros',  'KM'),
(51,    'Congo',    'CG'),
(52,    'Congo, Democratic Republic of the Congo',  'CD'),
(53,    'Cook Islands', 'CK'),
(54,    'Costa Rica',   'CR'),
(55,    'Cote D\'Ivoire',   'CI'),
(56,    'Croatia',  'HR'),
(57,    'Cuba', 'CU'),
(58,    'Curacao',  'CW'),
(59,    'Cyprus',   'CY'),
(60,    'Czech Republic',   'CZ'),
(61,    'Denmark',  'DK'),
(62,    'Djibouti', 'DJ'),
(63,    'Dominica', 'DM'),
(64,    'Dominican Republic',   'DO'),
(65,    'Ecuador',  'EC'),
(66,    'Egypt',    'EG'),
(67,    'El Salvador',  'SV'),
(68,    'Equatorial Guinea',    'GQ'),
(69,    'Eritrea',  'ER'),
(70,    'Estonia',  'EE'),
(71,    'Ethiopia', 'ET'),
(72,    'Falkland Islands (Malvinas)',  'FK'),
(73,    'Faroe Islands',    'FO'),
(74,    'Fiji', 'FJ'),
(75,    'Finland',  'FI'),
(76,    'France',   'FR'),
(77,    'French Guiana',    'GF'),
(78,    'French Polynesia', 'PF'),
(79,    'French Southern Territories',  'TF'),
(80,    'Gabon',    'GA'),
(81,    'Gambia',   'GM'),
(82,    'Georgia',  'GE'),
(83,    'Germany',  'DE'),
(84,    'Ghana',    'GH'),
(85,    'Gibraltar',    'GI'),
(86,    'Greece',   'GR'),
(87,    'Greenland',    'GL'),
(88,    'Grenada',  'GD'),
(89,    'Guadeloupe',   'GP'),
(90,    'Guam', 'GU'),
(91,    'Guatemala',    'GT'),
(92,    'Guernsey', 'GG'),
(93,    'Guinea',   'GN'),
(94,    'Guinea-Bissau',    'GW'),
(95,    'Guyana',   'GY'),
(96,    'Haiti',    'HT'),
(97,    'Heard Island and Mcdonald Islands',    'HM'),
(98,    'Holy See (Vatican City State)',    'VA'),
(99,    'Honduras', 'HN'),
(100,   'Hong Kong',    'HK'),
(101,   'Hungary',  'HU'),
(102,   'Iceland',  'IS'),
(103,   'India',    'IN'),
(104,   'Indonesia',    'ID'),
(105,   'Iran, Islamic Republic of',    'IR'),
(106,   'Iraq', 'IQ'),
(107,   'Ireland',  'IE'),
(108,   'Isle of Man',  'IM'),
(109,   'Israel',   'IL'),
(110,   'Italy',    'IT'),
(111,   'Jamaica',  'JM'),
(112,   'Japan',    'JP'),
(113,   'Jersey',   'JE'),
(114,   'Jordan',   'JO'),
(115,   'Kazakhstan',   'KZ'),
(116,   'Kenya',    'KE'),
(117,   'Kiribati', 'KI'),
(118,   'Korea, Democratic People\'s Republic of',  'KP'),
(119,   'Korea, Republic of',   'KR'),
(120,   'Kosovo',   'XK'),
(121,   'Kuwait',   'KW'),
(122,   'Kyrgyzstan',   'KG'),
(123,   'Lao People\'s Democratic Republic',    'LA'),
(124,   'Latvia',   'LV'),
(125,   'Lebanon',  'LB'),
(126,   'Lesotho',  'LS'),
(127,   'Liberia',  'LR'),
(128,   'Libyan Arab Jamahiriya',   'LY'),
(129,   'Liechtenstein',    'LI'),
(130,   'Lithuania',    'LT'),
(131,   'Luxembourg',   'LU'),
(132,   'Macao',    'MO'),
(133,   'Macedonia, the Former Yugoslav Republic of',   'MK'),
(134,   'Madagascar',   'MG'),
(135,   'Malawi',   'MW'),
(136,   'Malaysia', 'MY'),
(137,   'Maldives', 'MV'),
(138,   'Mali', 'ML'),
(139,   'Malta',    'MT'),
(140,   'Marshall Islands', 'MH'),
(141,   'Martinique',   'MQ'),
(142,   'Mauritania',   'MR'),
(143,   'Mauritius',    'MU'),
(144,   'Mayotte',  'YT'),
(145,   'Mexico',   'MX'),
(146,   'Micronesia, Federated States of',  'FM'),
(147,   'Moldova, Republic of', 'MD'),
(148,   'Monaco',   'MC'),
(149,   'Mongolia', 'MN'),
(150,   'Montenegro',   'ME'),
(151,   'Montserrat',   'MS'),
(152,   'Morocco',  'MA'),
(153,   'Mozambique',   'MZ'),
(154,   'Myanmar',  'MM'),
(155,   'Namibia',  'NA'),
(156,   'Nauru',    'NR'),
(157,   'Nepal',    'NP'),
(158,   'Netherlands',  'NL'),
(159,   'Netherlands Antilles', 'AN'),
(160,   'New Caledonia',    'NC'),
(161,   'New Zealand',  'NZ'),
(162,   'Nicaragua',    'NI'),
(163,   'Niger',    'NE'),
(164,   'Nigeria',  'NG'),
(165,   'Niue', 'NU'),
(166,   'Norfolk Island',   'NF'),
(167,   'Northern Mariana Islands', 'MP'),
(168,   'Norway',   'NO'),
(169,   'Oman', 'OM'),
(170,   'Pakistan', 'PK'),
(171,   'Palau',    'PW'),
(172,   'Palestinian Territory, Occupied',  'PS'),
(173,   'Panama',   'PA'),
(174,   'Papua New Guinea', 'PG'),
(175,   'Paraguay', 'PY'),
(176,   'Peru', 'PE'),
(177,   'Philippines',  'PH'),
(178,   'Pitcairn', 'PN'),
(179,   'Poland',   'PL'),
(180,   'Portugal', 'PT'),
(181,   'Puerto Rico',  'PR'),
(182,   'Qatar',    'QA'),
(183,   'Reunion',  'RE'),
(184,   'Romania',  'RO'),
(185,   'Russian Federation',   'RU'),
(186,   'Rwanda',   'RW'),
(187,   'Saint Barthelemy', 'BL'),
(188,   'Saint Helena', 'SH'),
(189,   'Saint Kitts and Nevis',    'KN'),
(190,   'Saint Lucia',  'LC'),
(191,   'Saint Martin', 'MF'),
(192,   'Saint Pierre and Miquelon',    'PM'),
(193,   'Saint Vincent and the Grenadines', 'VC'),
(194,   'Samoa',    'WS'),
(195,   'San Marino',   'SM'),
(196,   'Sao Tome and Principe',    'ST'),
(197,   'Saudi Arabia', 'SA'),
(198,   'Senegal',  'SN'),
(199,   'Serbia',   'RS'),
(200,   'Serbia and Montenegro',    'CS'),
(201,   'Seychelles',   'SC'),
(202,   'Sierra Leone', 'SL'),
(203,   'Singapore',    'SG'),
(204,   'Sint Maarten', 'SX'),
(205,   'Slovakia', 'SK'),
(206,   'Slovenia', 'SI'),
(207,   'Solomon Islands',  'SB'),
(208,   'Somalia',  'SO'),
(209,   'South Africa', 'ZA'),
(210,   'South Georgia and the South Sandwich Islands', 'GS'),
(211,   'South Sudan',  'SS'),
(212,   'Spain',    'ES'),
(213,   'Sri Lanka',    'LK'),
(214,   'Sudan',    'SD'),
(215,   'Suriname', 'SR'),
(216,   'Svalbard and Jan Mayen',   'SJ'),
(217,   'Swaziland',    'SZ'),
(218,   'Sweden',   'SE'),
(219,   'Switzerland',  'CH'),
(220,   'Syrian Arab Republic', 'SY'),
(221,   'Taiwan, Province of China',    'TW'),
(222,   'Tajikistan',   'TJ'),
(223,   'Tanzania, United Republic of', 'TZ'),
(224,   'Thailand', 'TH'),
(225,   'Timor-Leste',  'TL'),
(226,   'Togo', 'TG'),
(227,   'Tokelau',  'TK'),
(228,   'Tonga',    'TO'),
(229,   'Trinidad and Tobago',  'TT'),
(230,   'Tunisia',  'TN'),
(231,   'Turkey',   'TR'),
(232,   'Turkmenistan', 'TM'),
(233,   'Turks and Caicos Islands', 'TC'),
(234,   'Tuvalu',   'TV'),
(235,   'Uganda',   'UG'),
(236,   'Ukraine',  'UA'),
(237,   'United Arab Emirates', 'AE'),
(238,   'United Kingdom',   'GB'),
(239,   'United States',    'US'),
(240,   'United States Minor Outlying Islands', 'UM'),
(241,   'Uruguay',  'UY'),
(242,   'Uzbekistan',   'UZ'),
(243,   'Vanuatu',  'VU'),
(244,   'Venezuela',    'VE'),
(245,   'Viet Nam', 'VN'),
(246,   'Virgin Islands, British',  'VG'),
(247,   'Virgin Islands, U.s.', 'VI'),
(248,   'Wallis and Futuna',    'WF'),
(249,   'Western Sahara',   'EH'),
(250,   'Yemen',    'YE'),
(251,   'Zambia',   'ZM'),
(252,   'Zimbabwe', 'ZW');

");
}

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
