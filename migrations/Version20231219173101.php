<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231219173101 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'add global & local profiles menus';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('
                    SELECT parent_id INTO @PARENT_ID FROM `menu` WHERE label = "menu_profiles";
                    UPDATE `menu` SET `route_name` = "profile_global_list",  `label` = "menu_global_profiles" WHERE `menu`.`label` = "menu_profiles";
                    INSERT INTO `menu` (`parent_id`, `label`, `icon_class`, `route_name`, `parameters`) VALUES
                    (@PARENT_ID, "menu_local_profiles", "far fa-circle", "profile_local_list", NULL);
        ');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
