<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230612142623 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
            INSERT INTO `media_directory` (`id`, `site_id`, `parent_directory_id`, `label`, `path`, `read_only`) VALUES (1, NULL, NULL, 'Racine', 'Racine', 1);
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'AP', 'Racine > AP', 1, 'AP');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'AC', 'Racine > AC', 1, 'AC');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'DS', 'Racine > DS', 1, 'DS');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'OP', 'Racine > OP', 1, 'OP');
            INSERT INTO `media_directory` (`site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`) VALUES (NULL, 1, 'VX', 'Racine > VX', 1, 'VX');
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}