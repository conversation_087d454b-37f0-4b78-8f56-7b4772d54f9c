<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240904145601 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
          SELECT id INTO @FEATURES_MENU_ID FROM `menu` WHERE label = 'features';
  
          INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`) 
          VALUES (NULL, @FEATURES_MENU_ID, 'global_features', 'far fa-circle', NULL, NULL, NULL);
  
          INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`) 
          VALUES (NULL, @FEATURES_MENU_ID, 'brand_features', 'far fa-circle', NULL, NULL, NULL);
  
          INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`) 
          VALUES (NULL, @FEATURES_MENU_ID, 'local_features', 'far fa-circle', NULL, NULL, NULL);
  
          SELECT id INTO @GLOBAL_FEATURES_MENU_ID FROM `menu` WHERE label = 'global_features';
          SELECT id INTO @BRAND_FEATURES_MENU_ID FROM `menu` WHERE label = 'brand_features';
          SELECT id INTO @LOCAL_FEATURES_MENU_ID FROM `menu` WHERE label = 'local_features';
  
          SELECT id INTO @LOCAL_ADMIN_ID FROM `role` WHERE label = 'Local Technical Administrator';
          SELECT id INTO @SUPER_ADMIN_ID FROM `role` WHERE label = 'Super Administrator';
          SELECT id INTO @TECHNICAL_ADMIN_ID FROM `role` WHERE label = 'Technical Administrator';
          SELECT id INTO @FUNCTIONAL_ADMIN_ID FROM `role` WHERE label = 'Functional Administrator';
          SELECT id INTO @READER_ID FROM `role` WHERE label = 'Reader';
          SELECT id INTO @OPERATIONS_ID FROM `role` WHERE label = 'Operations';
          SELECT id INTO @WEBMASTER_ID FROM `role` WHERE label = 'Webmaster';
  
          UPDATE menu SET parent_id = @GLOBAL_FEATURES_MENU_ID WHERE parent_id = @FEATURES_MENU_ID;
          UPDATE menu SET parent_id = @FEATURES_MENU_ID WHERE id = @GLOBAL_FEATURES_MENU_ID;
          UPDATE menu SET parent_id = @FEATURES_MENU_ID WHERE id = @BRAND_FEATURES_MENU_ID;
          UPDATE menu SET parent_id = @FEATURES_MENU_ID WHERE id = @LOCAL_FEATURES_MENU_ID;
  
          INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`) 
          VALUES (NULL, @SUPER_ADMIN_ID, @GLOBAL_FEATURES_MENU_ID, 'W'),
          (NULL, @TECHNICAL_ADMIN_ID, @BRAND_FEATURES_MENU_ID, 'R'),
          (NULL, @FUNCTIONAL_ADMIN_ID, @BRAND_FEATURES_MENU_ID, 'W'),
          (NULL, @LOCAL_ADMIN_ID, @LOCAL_FEATURES_MENU_ID, 'W'),
          (NULL, @READER_ID, @LOCAL_FEATURES_MENU_ID, 'R'),
          (NULL, @OPERATIONS_ID, @LOCAL_FEATURES_MENU_ID, 'R'),
          (NULL, @WEBMASTER_ID, @LOCAL_FEATURES_MENU_ID, 'R');
  
          ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
