<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241022094022 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE language set label="Albanian" where code="sq"');
        $this->addSql('UPDATE language set label="German" where label="de"');
        $this->addSql('UPDATE language set label="English" where label="en"');
        $this->addSql('UPDATE language set label="Arabic" where label="ar"');
        $this->addSql('UPDATE language set label="Bulgarian" where label="bg"');
        $this->addSql('UPDATE language set label="Chinese" where label="zh"');
        $this->addSql('UPDATE language set label="Korean" where label="ko"');
        $this->addSql('UPDATE language set label="Croatian" where label="hr"');
        $this->addSql('UPDATE language set label="Danish" where label="da"');
        $this->addSql('UPDATE language set label="Spanish" where label="es"');
        $this->addSql('UPDATE language set label="Estonian" where label="ee"');
        $this->addSql('UPDATE language set label="French" where label="fr"');
        $this->addSql('UPDATE language set label="Georgian" where label="ka"');
        $this->addSql('UPDATE language set label="Greek" where label="el"');
        $this->addSql('UPDATE language set label="Hebrew" where label="il"');
        $this->addSql('UPDATE language set label="Hindi" where label="hi"');
        $this->addSql('UPDATE language set label="Hungarian" where label="hu"');
        $this->addSql('UPDATE language set label="Indonesian" where label="id"');
        $this->addSql('UPDATE language set label="Irish" where label="ga"');
        $this->addSql('UPDATE language set label="Icelandic" where label="is"');
        $this->addSql('UPDATE language set label="Italian" where label="it"');
        $this->addSql('UPDATE language set label="Japanese" where label="ja"');
        $this->addSql('UPDATE language set label="Lao" where label="lo"');
        $this->addSql('UPDATE language set label="Latvian" where label="lv"');
        $this->addSql('UPDATE language set label="Lithuanian" where label="lt"');
        $this->addSql('UPDATE language set label="Malagasy" where label="mg"');
        $this->addSql('UPDATE language set label="Dutch" where label="nl"');
        $this->addSql('UPDATE language set label="Norwegian" where label="no"');
        $this->addSql('UPDATE language set label="Polish" where label="pl"');
        $this->addSql('UPDATE language set label="Portuguese" where label="pt"');
        $this->addSql('UPDATE language set label="Romanian" where label="ro"');
        $this->addSql('UPDATE language set label="Russian" where label="ru"');
        $this->addSql('UPDATE language set label="Slovak" where label="sk"');
        $this->addSql('UPDATE language set label="Slovenian" where label="sl"');
        $this->addSql('UPDATE language set label="Swedish" where label="sv"');
        $this->addSql('UPDATE language set label="Czech" where label="cs"');
        $this->addSql('UPDATE language set label="Thai" where label="th"');
        $this->addSql('UPDATE language set label="Turkish" where label="tr"');
        $this->addSql('UPDATE language set label="Ukrainian" where label="uk"');
        $this->addSql('UPDATE language set label="Vietnamese" where label="vi"');
        $this->addSql('UPDATE language set label="Walloon" where label="wa"');
    }

    public function down(Schema $schema): void
    {
        
    }
}
