<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250120143226 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE feature_access DROP FOREIGN KEY FK_7F7CCDFAD60322AC');
        $this->addSql('ALTER TABLE feature_access DROP FOREIGN KEY FK_7F7CCDFAF08250DA');
        $this->addSql('DROP TABLE feature_access');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE feature_access (id INT AUTO_INCREMENT NOT NULL, feature_setting_id INT NOT NULL, role_id INT NOT NULL, mode VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, INDEX IDX_7F7CCDFAD60322AC (role_id), INDEX IDX_7F7CCDFAF08250DA (feature_setting_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE feature_access ADD CONSTRAINT FK_7F7CCDFAD60322AC FOREIGN KEY (role_id) REFERENCES role (id)');
        $this->addSql('ALTER TABLE feature_access ADD CONSTRAINT FK_7F7CCDFAF08250DA FOREIGN KEY (feature_setting_id) REFERENCES feature_setting (id)');
    }
}
