<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230717115520 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("           
        INSERT INTO `culture` (`id`, `code`) VALUES
        (1, 'af-AF'),
        (2, 'sv-AX'),
        (3, 'sq-AL'),
        (4, 'ar-DZ'),
        (5, 'en-AS'),
        (6, 'ca-AD'),
        (7, 'pt-AO'),
        (8, 'nl-AW'),
        (9, 'en-AQ'),
        (10, 'en-AG'),
        (11, 'es-AR'),
        (12, 'hy-AM'),
        (13, 'en-AW'),
        (14, 'en-AU'),
        (15, 'de-AT'),
        (17, 'ar-BH'),
        (18, 'bn-BD'),
        (19, 'BY-be'),
        (20, 'bs-BA'),
        (21, 'bg-BG'),
        (22, 'ca-ES'),
        (23, 'zh-TW'),
        (24, 'hr-HR'),
        (25, 'cs-CZ'),
        (26, 'da-DK'),
        (27, 'fi-FI'),
        (28, 'fr-FR'),
        (29, 'ka-GE'),
        (30, 'de-DE'),
        (31, 'el-GR'),
        (32, 'en-US'),
        (33, 'hu-HU'),
        (34, 'is-IS'),
        (35, 'hi-IN'),
        (36, 'ga-IE'),
        (37, 'it-IT'),
        (38, 'ja-JP'),
        (39, 'ko-KR'),
        (40, 'lv-LV'),
        (41, 'lt-LT'),
        (42, 'mt-MT'),
        (43, 'no-NO'),
        (44, 'pl-PL'),
        (45, 'pt-PT'),
        (46, 'ro-RO'),
        (47, 'ru-RU'),
        (48, 'sr-RS'),
        (49, 'sk-SK'),
        (50, 'sl-SI'),
        (51, 'es-ES'),
        (52, 'sv-SE'),
        (53, 'th-TH'),
        (54, 'tr-TR'),
        (55, 'uk-UA'),
        (56, 'ar-AE'),
        (57, 'vi-VN'),
        (58, 'ar-YE'),
        (59, 'mi-NZ'),
        (60, 'en-TT'),
        (61, 'ar-MA'),
        (62, 'en-GB');
            ");

    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}