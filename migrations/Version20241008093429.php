<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241008093429 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DELETE FROM role_menu WHERE menu_id IN (SELECT id FROM menu WHERE label = "select_demo")');
        $this->addSql('DELETE FROM profile_menu WHERE menu_id IN (SELECT id FROM menu WHERE label = "select_demo")');
        $this->addSql('DELETE FROM menu WHERE label = "select_demo"');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
