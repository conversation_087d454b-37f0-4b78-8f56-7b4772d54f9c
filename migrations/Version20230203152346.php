<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230203152346 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE site ADD country_id INT NULL, DROP country');
        $this->addSql('ALTER TABLE site ADD CONSTRAINT FK_694309E4F92F3E70 FOREIGN KEY (country_id) REFERENCES country (id)');
        $this->addSql('CREATE INDEX IDX_694309E4F92F3E70 ON site (country_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE site DROP FOREIGN KEY FK_694309E4F92F3E70');
        $this->addSql('DROP INDEX IDX_694309E4F92F3E70 ON site');
        $this->addSql('ALTER TABLE site ADD country VARCHAR(2) DEFAULT NULL, DROP country_id');
    }
}
