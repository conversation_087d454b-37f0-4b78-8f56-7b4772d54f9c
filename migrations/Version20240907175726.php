<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240907175726 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE role ADD name VARCHAR(255) DEFAULT NULL');
        $this->addSql("
            UPDATE role SET name = 'super_admin' WHERE label = 'Super Administrator';
            UPDATE role SET name = 'technical_admin' WHERE label = 'Technical Administrator';
            UPDATE role SET name = 'functional_admin' WHERE label = 'Functional Administrator';
            UPDATE role SET name = 'reader' WHERE label = 'Reader';
            UPDATE role SET name = 'operations' WHERE label = 'Operations';
            UPDATE role SET name = 'webmaster' WHERE label = 'webmaster';
            UPDATE role SET name = 'local_technical_admin' WHERE label = 'Local Technical Administrator';
        ");
       
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE role DROP name');
    }
}
