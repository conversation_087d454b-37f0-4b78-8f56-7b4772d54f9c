<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241223090309 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $rows = $this->connection->fetchAllAssociative('SELECT id, code FROM brand WHERE code <> :condition', [
            'condition' => 'XX',
        ]);

        $languages = $this->connection->fetchAllAssociative("SELECT id FROM language WHERE code IN ('fr', 'it', 'en', 'es')");

        $roles = $this->connection->fetchAllAssociative("SELECT id FROM role WHERE name IN ('technical_admin', 'functional_admin')");

        foreach($rows as $row) {   
            $insertedLine = $this->connection->fetchAssociative("SELECT id FROM site WHERE brand_id = :brandId AND label = :label", [
                'brandId' => $row['id'],
                'label' => 'ADMINISTRATION CENTRAL ' . $row['code']
            ]);

            foreach($roles as $role) {
                $this->addSql('INSERT INTO profile
                            (site_id, role_id, profile_admin, brand_id)
                            VALUES(:insertedId, :role, 0, :brandId);', [
                    'insertedId' => $insertedLine['id'], 
                    'brandId'    => $row['id'],
                    'role'       => $role['id']
                ]);
            }

            foreach($languages as $lang) {
                $this->addSql('INSERT INTO site_language
                            (site_id, language_id)
                            VALUES(:insertedId, :languageId);', [
                    'insertedId' => $insertedLine['id'], 
                    'languageId' => $lang['id'], 
                ]);
            }
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        // Delete rows added to `profile`
        $this->addSql('DELETE FROM profile WHERE site_id IN (SELECT id FROM site WHERE label LIKE :prefix)', [
            'prefix' => 'ADMINISTRATION CENTRAL %',
        ]);

        $this->addSql('DELETE FROM site_language WHERE site_id IN (SELECT id FROM site WHERE label LIKE :prefix)', [
            'prefix' => 'ADMINISTRATION CENTRAL %',
        ]);
    }
}
