<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230919135732 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE site CHANGE COLUMN country_id country_id INT(11) NULL DEFAULT NULL');
        $this->addSql('UPDATE site SET country_id=NULL WHERE  label="ADMINISTRATION CENTRAL"');
    }

    public function down(Schema $schema): void
    {
    }
}
