<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241105063944 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // Update the label of the site entity to make it unique
        $this->addSql('UPDATE site
            JOIN (
                SELECT label
                FROM site
                GROUP BY label
                HAVING COUNT(label) > 1
            ) AS duplicates ON site.label = duplicates.label
            SET site.label = CONCAT(site.label, \' \', site.id);');

        // Add unique constraints for the site entity
        $this->addSql('CREATE UNIQUE INDEX unique_label ON site (label)');
    }

    public function down(Schema $schema): void
    {
        // Remove unique constraints for the site entity
        $this->addSql('DROP INDEX unique_label ON site');
    }
}
