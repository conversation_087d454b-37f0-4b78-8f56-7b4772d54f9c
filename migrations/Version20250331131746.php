<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250331131746 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE static_page (id INT AUTO_INCREMENT NOT NULL, brand_id INT NOT NULL, channel_id INT NOT NULL, language_id INT DEFAULT NULL, country_id INT NOT NULL, page_title VARCHAR(255) NOT NULL, status VARCHAR(255) NOT NULL, INDEX IDX_8FA4EF9544F5D008 (brand_id), INDEX IDX_8FA4EF9572F5A1AA (channel_id), INDEX IDX_8FA4EF9582F1BAF4 (language_id), INDEX IDX_8FA4EF95F92F3E70 (country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE static_page ADD CONSTRAINT FK_8FA4EF9544F5D008 FOREIGN KEY (brand_id) REFERENCES brand (id)');
        $this->addSql('ALTER TABLE static_page ADD CONSTRAINT FK_8FA4EF9572F5A1AA FOREIGN KEY (channel_id) REFERENCES channel (id)');
        $this->addSql('ALTER TABLE static_page ADD CONSTRAINT FK_8FA4EF9582F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE static_page ADD CONSTRAINT FK_8FA4EF95F92F3E70 FOREIGN KEY (country_id) REFERENCES country (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE static_page DROP FOREIGN KEY FK_8FA4EF9544F5D008');
        $this->addSql('ALTER TABLE static_page DROP FOREIGN KEY FK_8FA4EF9572F5A1AA');
        $this->addSql('ALTER TABLE static_page DROP FOREIGN KEY FK_8FA4EF9582F1BAF4');
        $this->addSql('ALTER TABLE static_page DROP FOREIGN KEY FK_8FA4EF95F92F3E70');
        $this->addSql('DROP TABLE static_page');
    }
}
