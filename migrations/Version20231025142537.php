<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231025142537 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE translation_key ADD widget_id INT DEFAULT NULL AFTER feature');
        $this->addSql('ALTER TABLE translation_key ADD CONSTRAINT FK_AADCBD56FBE885E2 FOREIGN KEY (widget_id) REFERENCES widget (id)');
        $this->addSql('CREATE INDEX IDX_AADCBD56FBE885E2 ON translation_key (widget_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE translation_key DROP FOREIGN KEY FK_AADCBD56FBE885E2');
        $this->addSql('DROP INDEX IDX_AADCBD56FBE885E2 ON translation_key');
        $this->addSql('ALTER TABLE translation_key DROP widget_id');
    }
}
