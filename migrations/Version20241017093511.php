<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241017093511 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE language set code="ar" where label="Arabe"');
        $this->addSql('UPDATE language set code="ga" where label="Irlandais"');
        $this->addSql('UPDATE language set code="he" where label="<PERSON><PERSON>breu"');
        $this->addSql('UPDATE language set code="hi" where label="Hindi"');
        $this->addSql('UPDATE language set code="hr" where label="Croate"');
        $this->addSql('UPDATE language set code="ja" where label="Japonais"');
        $this->addSql('UPDATE language set code="ka" where label="Georgien"');
        $this->addSql('UPDATE language set code="ko" where label="Coreen"');
        $this->addSql('UPDATE language set code="lo" where label="Lao"');
        $this->addSql('UPDATE language set code="no" where label="Norvegien"');
        $this->addSql('UPDATE language set code="sq" where label="Albanais"');
        $this->addSql('UPDATE language set code="vi" where label="Vietnamien"');
        $this->addSql('UPDATE language set code="wa" where label="Wallon"');
        $this->addSql('UPDATE language set code="zh" where label="Chinois"');
    }

    public function down(Schema $schema): void
    {
        
    }
}
