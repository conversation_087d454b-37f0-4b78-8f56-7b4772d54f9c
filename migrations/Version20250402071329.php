<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250402071329 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create the page menu';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
          SELECT id INTO @ACCESS_MENU_ID FROM `menu` WHERE label = 'menu_admin';
  
          INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`) 
          VALUES (NULL, @ACCESS_MENU_ID, 'Web Page','fas fa-globe', 'static_index', NULL, NULL);

          SELECT id INTO @WEB_PAGE_MENU_ID FROM `menu` WHERE label = 'Web Page';

          SELECT id INTO @SUPER_ADMIN_ID FROM `role` WHERE label = 'Super Administrator';

           INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`) 
          VALUES (NULL, @SUPER_ADMIN_ID, @WEB_PAGE_MENU_ID, 'W');

        ");
        
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql("
            DELETE FROM `role_menu` 
            WHERE role_id = (SELECT id FROM `role` WHERE label = 'Super Administrator') 
            AND menu_id = (SELECT id FROM `menu` WHERE label = 'Web Page');
            
            DELETE FROM `menu` 
            WHERE label = 'Web Page';
        ");
    }
}
