<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230223162707 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE locale_translation (id INT AUTO_INCREMENT NOT NULL, translation_key_id INT NOT NULL, site_id INT NOT NULL, language_id INT NOT NULL, last_update DATETIME DEFAULT NULL, translation VARCHAR(255) DEFAULT NULL, INDEX IDX_E6F7FB05D07ED992 (translation_key_id), INDEX IDX_E6F7FB05F6BD1646 (site_id), INDEX IDX_E6F7FB0582F1BAF4 (language_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE translation_key (id INT AUTO_INCREMENT NOT NULL, reference_translations_id INT DEFAULT NULL, brand_id INT NOT NULL, label_key VARCHAR(255) DEFAULT NULL, feature VARCHAR(255) DEFAULT NULL, channel VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_AADCBD5679D01268 (reference_translations_id), INDEX IDX_AADCBD5644F5D008 (brand_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE locale_translation ADD CONSTRAINT FK_E6F7FB05D07ED992 FOREIGN KEY (translation_key_id) REFERENCES translation_key (id)');
        $this->addSql('ALTER TABLE locale_translation ADD CONSTRAINT FK_E6F7FB05F6BD1646 FOREIGN KEY (site_id) REFERENCES site (id)');
        $this->addSql('ALTER TABLE locale_translation ADD CONSTRAINT FK_E6F7FB0582F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE translation_key ADD CONSTRAINT FK_AADCBD5679D01268 FOREIGN KEY (reference_translations_id) REFERENCES locale_translation (id)');
        $this->addSql('ALTER TABLE translation_key ADD CONSTRAINT FK_AADCBD5644F5D008 FOREIGN KEY (brand_id) REFERENCES brand (id)');
        
        $this->addSql('ALTER TABLE language ADD reference TINYINT(1) DEFAULT NULL');
        $this->addSql('ALTER TABLE role CHANGE label label VARCHAR(255) DEFAULT \'[-Sans nom-]\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE locale_translation DROP FOREIGN KEY FK_E6F7FB05D07ED992');
        $this->addSql('ALTER TABLE locale_translation DROP FOREIGN KEY FK_E6F7FB05F6BD1646');
        $this->addSql('ALTER TABLE locale_translation DROP FOREIGN KEY FK_E6F7FB0582F1BAF4');
        $this->addSql('ALTER TABLE translation_key DROP FOREIGN KEY FK_AADCBD5679D01268');
        $this->addSql('ALTER TABLE translation_key DROP FOREIGN KEY FK_AADCBD5644F5D008');
        $this->addSql('DROP TABLE locale_translation');
        $this->addSql('DROP TABLE translation_key');
        $this->addSql('ALTER TABLE language DROP reference');
        $this->addSql('ALTER TABLE role CHANGE label label VARCHAR(255) DEFAULT \'\' NOT NULL');
    }
}
