<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240503104327 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE local_key_json_release (id INT AUTO_INCREMENT NOT NULL, brand_id INT NOT NULL, language_id INT NOT NULL, country_id INT NOT NULL, channel VARCHAR(255) NOT NULL, updated_date DATETIME NOT NULL, imported_date DATETIME NOT NULL, released_date DATETIME NOT NULL, filename VARCHAR(255) NOT NULL, status VARCHAR(10) NOT NULL, INDEX IDX_8D67F01C44F5D008 (brand_id), INDEX IDX_8D67F01C82F1BAF4 (language_id), INDEX IDX_8D67F01CF92F3E70 (country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE local_key_json_release ADD CONSTRAINT FK_8D67F01C44F5D008 FOREIGN KEY (brand_id) REFERENCES brand (id)');
        $this->addSql('ALTER TABLE local_key_json_release ADD CONSTRAINT FK_8D67F01C82F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE local_key_json_release ADD CONSTRAINT FK_8D67F01CF92F3E70 FOREIGN KEY (country_id) REFERENCES country (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE local_key_json_release DROP FOREIGN KEY FK_8D67F01C44F5D008');
        $this->addSql('ALTER TABLE local_key_json_release DROP FOREIGN KEY FK_8D67F01C82F1BAF4');
        $this->addSql('ALTER TABLE local_key_json_release DROP FOREIGN KEY FK_8D67F01CF92F3E70');
        $this->addSql('DROP TABLE local_key_json_release');
    }
}
