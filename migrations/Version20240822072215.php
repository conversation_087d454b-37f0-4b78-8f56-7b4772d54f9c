<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240822072215 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create the Local Json release key for all sites';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO local_key_json_release (brand_id, language_id, country_id, channel, imported_date, updated_date, status, filename)
            SELECT
                s.brand_id,
                sl.language_id,
                s.country_id,
                \'WEB\' AS channel,
                NOW() AS imported_date,
                NOW() AS updated_date,
                \'imported\' AS status,
                \'\' AS filename
            FROM
                site s
            JOIN
                site_language sl
            ON
                s.id = sl.site_id
            LEFT JOIN
                local_key_json_release lk
            ON
                s.brand_id = lk.brand_id
                AND sl.language_id = lk.language_id
                AND s.country_id = lk.country_id
                AND lk.channel = \'WEB\'  -- Check for existing \'WEB\' records
            WHERE
                lk.brand_id IS NULL
                AND lk.language_id IS NULL
                AND lk.country_id IS NULL
        ');

        $this->addSql('
            INSERT INTO local_key_json_release (brand_id, language_id, country_id, channel, imported_date, updated_date, status, filename)
            SELECT
                s.brand_id,
                sl.language_id,
                s.country_id,
                \'APP\' AS channel,
                NOW() AS imported_date,
                NOW() AS updated_date,
                \'imported\' AS status,
                \'\' AS filename
            FROM
                site s
            JOIN
                site_language sl
            ON
                s.id = sl.site_id
            LEFT JOIN
                local_key_json_release lk
            ON
                s.brand_id = lk.brand_id
                AND sl.language_id = lk.language_id
                AND s.country_id = lk.country_id
                AND lk.channel = \'APP\'  -- Check for existing \'APP\' records
            WHERE
                lk.brand_id IS NULL
                AND lk.language_id IS NULL
                AND lk.country_id IS NULL
        ');
    }

    public function down(Schema $schema): void
    {

        $this->addSql('
            DELETE FROM local_key_json_release
            WHERE channel = \'WEB\'
            AND (brand_id, language_id, country_id) IN (
                SELECT s.brand_id, sl.language_id, s.country_id
                FROM site s
                JOIN site_language sl ON s.id = sl.site_id
            )
        ');

        $this->addSql('
            DELETE FROM local_key_json_release
            WHERE channel = \'APP\'
            AND (brand_id, language_id, country_id) IN (
                SELECT s.brand_id, sl.language_id, s.country_id
                FROM site s
                JOIN site_language sl ON s.id = sl.site_id
            )
        ');
    }
}
