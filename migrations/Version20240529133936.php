<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240529133936 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE access_log (
                id INT AUTO_INCREMENT NOT NULL, 
                action VARCHAR(255) NOT NULL, 
                entity_class VARCHAR(255) NOT NULL, 
                entity_id VARCHAR(255) DEFAULT NULL, 
                username VARCHAR(255) DEFAULT NULL, 
                route VARCHAR(255) DEFAULT NULL, 
                method VARCHAR(255) DEFAULT NULL, 
                values_before JSON NOT NULL, 
                values_after JSON NOT NULL, 
                timestamp DATETIME NOT NULL, 
            PRIMARY KEY(id)) 
            DEFAULT CHARACTER SET utf8mb4 
            COLLATE `utf8mb4_unicode_ci` 
            ENGINE = InnoDB'
        );
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE access_log');
    }
}
