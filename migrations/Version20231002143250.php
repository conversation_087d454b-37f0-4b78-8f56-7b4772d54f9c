<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231002143250 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'create role menu relation';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE role_menu (role_id INT NOT NULL, menu_id INT NOT NULL, INDEX IDX_C3BA4807D60322AC (role_id), INDEX IDX_C3BA4807CCD7E912 (menu_id), PRIMARY KEY(role_id, menu_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE role_menu ADD CONSTRAINT FK_C3BA4807D60322AC FOREIGN KEY (role_id) REFERENCES role (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE role_menu ADD CONSTRAINT FK_C3BA4807CCD7E912 FOREIGN KEY (menu_id) REFERENCES menu (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE role_menu DROP FOREIGN KEY FK_C3BA4807D60322AC');
        $this->addSql('ALTER TABLE role_menu DROP FOREIGN KEY FK_C3BA4807CCD7E912');
        $this->addSql('DROP TABLE role_menu');
    }
}
