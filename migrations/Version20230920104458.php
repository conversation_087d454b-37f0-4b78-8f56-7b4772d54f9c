<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230920104458 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE feature_setting (id INT AUTO_INCREMENT NOT NULL, file VARCHAR(255) DEFAULT NULL, name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE culture CHANGE code code VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE feature CHANGE brand brand VARCHAR(50) NOT NULL, CHANGE source source VARCHAR(50) NOT NULL, CHANGE enabled enabled TINYINT(1) NOT NULL, CHANGE is_early_adopter is_early_adopter TINYINT(1) NOT NULL');
        //$this->addSql('ALTER TABLE profile DROP FOREIGN KEY FK_profile_role');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE feature_setting');
        $this->addSql('ALTER TABLE culture CHANGE code code VARCHAR(5) DEFAULT NULL');
        $this->addSql('ALTER TABLE feature CHANGE brand brand VARCHAR(255) NOT NULL, CHANGE source source VARCHAR(255) NOT NULL, CHANGE enabled enabled TINYINT(1) DEFAULT NULL, CHANGE is_early_adopter is_early_adopter TINYINT(1) DEFAULT NULL');
    }
}
