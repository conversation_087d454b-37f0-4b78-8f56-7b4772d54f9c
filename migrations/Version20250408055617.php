<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250408055617 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create the log incident menu';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
          SELECT id INTO @ACCESS_MENU_ID FROM `menu` WHERE label = 'menu_admin';
  
          INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`) 
          VALUES (NULL, @ACCESS_MENU_ID, 'Log Incident','fas fa-globe', 'log_incident_list', NULL, NULL);

          SELECT id INTO @LOG_INCIDENT_MENU_ID FROM `menu` WHERE label = 'Log Incident';

          SELECT id INTO @SUPER_ADMIN_ID FROM `role` WHERE label = 'Super Administrator';

           INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`) 
          VALUES (NULL, @SUPER_ADMIN_ID, @LOG_INCIDENT_MENU_ID, 'W');

        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql("
            DELETE FROM `role_menu` 
            WHERE role_id = (SELECT id FROM `role` WHERE label = 'Super Administrator') 
            AND menu_id = (SELECT id FROM `menu` WHERE label = 'Log Incident');
            
            DELETE FROM `menu` 
            WHERE label = 'Log Incident';
        ");
    }
}
