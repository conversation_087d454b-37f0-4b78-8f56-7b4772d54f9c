<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240610154249 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adding 2 new reference languages for translations: es and de';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
            UPDATE language SET is_reference = 1 WHERE code IN('es', 'de');
        ");

        $languageCodes = ['it', 'es', 'en', 'de', 'fr'];
        foreach ($languageCodes as $code) {
            $this->addSql("
                INSERT INTO reference_translation (translation_key_id, language_id, updated_at)
                SELECT 
                    tk.id, 
                    (SELECT id from language WHERE code='{$code}'),
                    NULL
                FROM translation_key tk
                LEFT JOIN (
                    SELECT r.* 
                    FROM reference_translation r 
                    INNER JOIN language l ON l.id = r.language_id 
                    WHERE l.code = '{$code}'
                ) rt  ON tk.id = rt.translation_key_id
                WHERE rt.id IS NULL
            ");
        }

    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql("
            UPDATE language SET is_reference = 0 WHERE code IN('es', 'de');
            DELETE FROM reference_translation WHERE language_id IN (SELECT id FROM language WHERE code IN('es', 'de'));
        ");

    }
}
