<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240503124700 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE local_key_json_release MODIFY released_date DATETIME DEFAULT NULL');
        $this->addSql("
        INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`) 
        VALUES (NULL, '2', 'Reference Label Release', 'fas-fa circle', 'publish_list', NULL, NULL);");
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('UPDATE local_key_json_release SET released_date = \'1900-01-01 00:00:00\' WHERE released_date IS NULL;');
        $this->addSql('ALTER TABLE local_key_json_release MODIFY released_date DATETIME NOT NULL');
    }    

}
