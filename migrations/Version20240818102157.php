<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240818102157 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Updates parent_id for specific routes based on feature label and deletes specific menu entries, handling foreign key constraints.';
    }

    public function up(Schema $schema): void
    {
        // Update parent_id for rows where route_name = 'feature' based on the id of the 'features' label
        $this->addSql("
            UPDATE `menu` AS m1
            JOIN (
                SELECT `id`
                FROM `menu`
                WHERE `label` = 'features'
                LIMIT 1
            ) AS feature ON m1.`route_name` = 'feature'
            SET m1.`parent_id` = feature.`id`
        ");

        // Delete from profile_menu where menu_id is in the list of labels to be deleted
        $this->addSql("
            DELETE FROM `profile_menu`
            WHERE `menu_id` IN (
                SELECT `id`
                FROM `menu`
                WHERE `label` IN ('global_features', 'brand_features', 'local_features')
            )
        ");

        // Delete from role_menu where menu_id is in the list of labels to be deleted
        $this->addSql("
            DELETE FROM `role_menu`
            WHERE `menu_id` IN (
                SELECT `id`
                FROM `menu`
                WHERE `label` IN ('global_features', 'brand_features', 'local_features')
            )
        ");

        // Finally, delete rows with specific labels from menu
        $this->addSql("
            DELETE FROM `menu`
            WHERE `label` IN ('global_features', 'brand_features', 'local_features')
        ");
    }

    public function down(Schema $schema): void
    {
        // Reinsert rows into the menu table (adjust values if necessary)
        $this->addSql("INSERT INTO `menu` (`label`) VALUES ('global_features')");
        $this->addSql("INSERT INTO `menu` (`label`) VALUES ('brand_features')");
        $this->addSql("INSERT INTO `menu` (`label`) VALUES ('local_features')");

        // Handle potential rollbacks in related tables if needed (optional)
    }
}
