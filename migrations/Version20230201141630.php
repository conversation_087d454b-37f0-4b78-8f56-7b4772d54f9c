<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230201141630 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE brand (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) DEFAULT NULL, code VARCHAR(30) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE currency (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(5) DEFAULT NULL, name VA<PERSON>HA<PERSON>(100) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE language (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(2) DEFAULT NULL, label VARCHAR(50) DEFAULT NULL, translate VARCHAR(50) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE site (id INT AUTO_INCREMENT NOT NULL, brand_id INT NULL, prefered_language_id INT NOT NULL, label VARCHAR(255) DEFAULT NULL, country VARCHAR(2) NULL DEFAULT NULL, timezone VARCHAR(255) NOT NULL, ldap_code VARCHAR(255) DEFAULT NULL, domtom TINYINT(1) DEFAULT NULL, civilites_customer_at_active TINYINT(1) DEFAULT NULL, reversed_name_order TINYINT(1) DEFAULT NULL, distance_unit VARCHAR(10) DEFAULT NULL, consumption_unit VARCHAR(20) DEFAULT NULL, cost VARCHAR(20) DEFAULT NULL, volume_unit VARCHAR(10) DEFAULT NULL, date_format VARCHAR(10) DEFAULT NULL, hour_format VARCHAR(10) DEFAULT NULL, INDEX IDX_694309E444F5D008 (brand_id), INDEX IDX_694309E497E28A86 (prefered_language_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE site_language (site_id INT NOT NULL, language_id INT NOT NULL, INDEX IDX_D896CE6FF6BD1646 (site_id), INDEX IDX_D896CE6F82F1BAF4 (language_id), PRIMARY KEY(site_id, language_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE site_currency (site_id INT NOT NULL, currency_id INT NOT NULL, INDEX IDX_651B37E5F6BD1646 (site_id), INDEX IDX_651B37E538248176 (currency_id), PRIMARY KEY(site_id, currency_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE site ADD CONSTRAINT FK_694309E444F5D008 FOREIGN KEY (brand_id) REFERENCES brand (id)');
        $this->addSql('ALTER TABLE site ADD CONSTRAINT FK_694309E497E28A86 FOREIGN KEY (prefered_language_id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE site_language ADD CONSTRAINT FK_D896CE6FF6BD1646 FOREIGN KEY (site_id) REFERENCES site (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE site_language ADD CONSTRAINT FK_D896CE6F82F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE site_currency ADD CONSTRAINT FK_651B37E5F6BD1646 FOREIGN KEY (site_id) REFERENCES site (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE site_currency ADD CONSTRAINT FK_651B37E538248176 FOREIGN KEY (currency_id) REFERENCES currency (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE site DROP FOREIGN KEY FK_694309E444F5D008');
        $this->addSql('ALTER TABLE site DROP FOREIGN KEY FK_694309E497E28A86');
        $this->addSql('ALTER TABLE site_language DROP FOREIGN KEY FK_D896CE6FF6BD1646');
        $this->addSql('ALTER TABLE site_language DROP FOREIGN KEY FK_D896CE6F82F1BAF4');
        $this->addSql('ALTER TABLE site_currency DROP FOREIGN KEY FK_651B37E5F6BD1646');
        $this->addSql('ALTER TABLE site_currency DROP FOREIGN KEY FK_651B37E538248176');
        $this->addSql('DROP TABLE brand');
        $this->addSql('DROP TABLE currency');
        $this->addSql('DROP TABLE language');
        $this->addSql('DROP TABLE site');
        $this->addSql('DROP TABLE site_language');
        $this->addSql('DROP TABLE site_currency');
    }
}
