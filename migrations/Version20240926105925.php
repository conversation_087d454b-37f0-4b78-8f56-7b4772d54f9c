<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240926105925 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("ALTER TABLE locale_translation RENAME TO local_translation");
        $this->addSql('UPDATE menu SET label= "local_translation_management", route_name = "local_translation_list" WHERE label = "locale_translation_management"');
        $this->addSql('UPDATE menu SET route_name = "localtranslations_dashboard" WHERE label = "local_translation_dashboard"');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql("ALTER TABLE local_translation RENAME TO locale_translation");
        $this->addSql('UPDATE menu SET label = "locale_translation_management", route_name = "locale_translation_list" WHERE label = "local_translation_management"');
        $this->addSql('UPDATE menu SET label = "local_translation_dashboard" WHERE route_name = "localtranslations_dashboard"');
    }
}
