<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250408082216 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create the APP & WEB Global Directory';
    }

    public function up(Schema $schema): void
    {
        // Create APP Global Directory
        $this->addSql("
            INSERT INTO `media_directory` 
                (`id`, `site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`, `channel_id`, `name`) 
            VALUES (
                NULL, 
                (SELECT id FROM site WHERE label = 'ADMINISTRATION CENTRAL'), 
                (SELECT id FROM (SELECT id FROM media_directory WHERE path = 'APP > XX') AS temp), 
                'XX', 
                'APP > XX > XX', 
                1, 
                'XX', 
                (SELECT id FROM channel WHERE name = 'APP'), 
                'Global'
            )
        ");

        // Create WEB Global Directory
        $this->addSql("
            INSERT INTO `media_directory` 
                (`id`, `site_id`, `parent_directory_id`, `label`, `path`, `read_only`, `brand`, `channel_id`, `name`) 
            VALUES (
                NULL, 
                (SELECT id FROM site WHERE label = 'ADMINISTRATION CENTRAL'), 
                (SELECT id FROM (SELECT id FROM media_directory WHERE path = 'WEB > XX') AS temp), 
                'XX', 
                'WEB > XX > XX', 
                1, 
                'XX', 
                (SELECT id FROM channel WHERE name = 'WEB'), 
                'Global'
            )
        ");
    }

    public function down(Schema $schema): void
    {
        // Remove APP Global Directory
        $this->addSql("
            DELETE FROM `media_directory`
            WHERE path = 'APP > XX > XX'
              AND name = 'Global'
        ");

        // Remove WEB Global Directory
        $this->addSql("
            DELETE FROM `media_directory`
            WHERE path = 'WEB > XX > XX'
              AND name = 'Global'
        ");
    }
}
