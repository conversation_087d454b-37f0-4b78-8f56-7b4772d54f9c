<?php
declare(strict_types=1);
namespace DoctrineMigrations;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
/**
* Auto-generated Migration: Please modify to your needs!
*/
final class Version20231109170057 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $menuItems = [
            ['id' => 4, 'route_name' => 'profile_manage_list'],
            ['id' => 7, 'route_name' => 'idp_role_index'],
            ['id' => 8, 'route_name' => 'feature_setting_index'],
            ['id' => 12, 'route_name' => 'feature'],
            ['id' => 16, 'route_name' => 'feature'],
            ['id' => 19, 'route_name' => 'feature'],
            ['id' => 23, 'route_name' => 'feature'],
            ['id' => 24, 'route_name' => 'feature'],
            ['id' => 25, 'route_name' => 'feature'],
            ['id' => 18, 'route_name' => 'role_manage_list'],
            ['id' => 27, 'route_name' => 'widget_admin_list'],
            ['id' => 29, 'route_name' => 'widget_management_list'],
        ];
 
        foreach ($menuItems as $menuItem) {
            $this->addSql("
                UPDATE `menu`
                SET `route_name` = '{$menuItem['route_name']}'
                WHERE `id` = {$menuItem['id']}
            ");
        }
    }
    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}