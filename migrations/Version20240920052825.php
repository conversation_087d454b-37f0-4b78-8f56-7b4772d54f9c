<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240920052825 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('UPDATE menu set label="settings_release" where label="Settings Release"');
        $this->addSql('UPDATE menu set label="reference_label_release" where label="Reference Label Release"');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('UPDATE menu set label="Settings Release" where label="settings_release"');
        $this->addSql('UPDATE menu set label="Reference Label Release" where label="reference_label_release"');
    }
}
