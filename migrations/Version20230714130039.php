<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230714130039 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
            INSERT INTO media_directory (site_id, parent_directory_id, label, path, READ_ONLY,brand)
            SELECT ps.id, md.id, c.code, CONCAT(md.path, ' > ', c.code), 1,b.code
            FROM media_directory md, site ps, country c , brand b 
            WHERE ps.country_id = c.id AND ps.brand_id = b.id and md.brand = b.code AND NOT EXISTS(
            SELECT md2.id
            FROM media_directory md2
            WHERE md2.path = CONCAT(md.path, ' > ', c.code))
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
