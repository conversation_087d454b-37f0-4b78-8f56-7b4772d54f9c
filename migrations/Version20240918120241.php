<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240918120241 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add Global as Country';
    }

    public function up(Schema $schema): void
    {
        // This method is automatically generated, but make sure it fits your application
        $this->addSql("INSERT INTO `country` (`name`, `code`) VALUES ('Global', 'XX')");

        // Update the `site` country_id based on the new 'Global' country
        $this->addSql("
            UPDATE `site` 
            SET `country_id` = (SELECT id FROM `country` WHERE `code` = 'XX') 
            WHERE `brand_id` = (SELECT id FROM `brand` WHERE `code` = 'XX')
        ");

        $this->addSql('
            INSERT INTO local_key_json_release (brand_id, language_id, country_id, channel, imported_date, updated_date, status, filename)
            SELECT
                s.brand_id,
                sl.language_id,
                s.country_id,
                \'WEB\' AS channel,
                NOW() AS imported_date,
                NOW() AS updated_date,
                \'imported\' AS status,
                \'\' AS filename
            FROM
                site s
            JOIN
                site_language sl ON s.id = sl.site_id
            WHERE
                s.label = \'ADMINISTRATION\'
                AND s.country_id = (SELECT id FROM `country` WHERE `code` = \'XX\')
                AND s.brand_id = (SELECT id FROM `brand` WHERE `code` = \'XX\')
        ');

        // Insert into local_key_json_release for APP channel
        $this->addSql('
            INSERT INTO local_key_json_release (brand_id, language_id, country_id, channel, imported_date, updated_date, status, filename)
            SELECT
                s.brand_id,
                sl.language_id,
                s.country_id,
                \'APP\' AS channel,
                NOW() AS imported_date,
                NOW() AS updated_date,
                \'imported\' AS status,
                \'\' AS filename
            FROM
                site s
            JOIN
                site_language sl ON s.id = sl.site_id
            WHERE
                s.label = \'ADMINISTRATION\'
                AND s.country_id = (SELECT id FROM `country` WHERE `code` = \'XX\')
                AND s.brand_id = (SELECT id FROM `brand` WHERE `code` = \'XX\')
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql("
            DELETE FROM local_key_json_release 
            WHERE country_id = (SELECT id FROM `country` WHERE `code` = 'XX')
            AND brand_id = (SELECT id FROM `brand` WHERE `code` = 'XX')
        ");
    
        $this->addSql("
            UPDATE `site` 
            SET `country_id` = NULL 
            WHERE `country_id` = (SELECT id FROM `country` WHERE `code` = 'XX')
        ");
    
        $this->addSql("DELETE FROM `country` WHERE `code` = 'XX'");
    }
    
}
