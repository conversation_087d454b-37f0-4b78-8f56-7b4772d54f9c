<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231011134124 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE role_menu DROP FOREIGN KEY FK_C3BA4807CCD7E912');
        $this->addSql('ALTER TABLE role_menu DROP FOREIGN KEY FK_C3BA4807D60322AC');
        $this->addSql('DROP INDEX IDX_C3BA4807CCD7E912 ON role_menu');
        $this->addSql('ALTER TABLE role_menu ADD id INT AUTO_INCREMENT NOT NULL FIRST, ADD permission VARCHAR(255) DEFAULT NULL, CHANGE menu_id menu_id INT NOT NULL, DROP PRIMARY KEY, ADD PRIMARY KEY (id)');
        $this->addSql('ALTER TABLE role_menu ADD CONSTRAINT FK_C3BA480714041B84 FOREIGN KEY (menu_id) REFERENCES menu (id)');
        $this->addSql('ALTER TABLE role_menu ADD CONSTRAINT FK_C3BA4807D60322AC FOREIGN KEY (role_id) REFERENCES role (id)');
        $this->addSql('CREATE INDEX IDX_C3BA480714041B84 ON role_menu (menu_id)');
        $this->addSql('UPDATE role_menu SET permission = "W"');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE role_menu MODIFY id INT NOT NULL');
        $this->addSql('ALTER TABLE role_menu DROP FOREIGN KEY FK_C3BA480714041B84');
        $this->addSql('ALTER TABLE role_menu DROP FOREIGN KEY FK_C3BA4807D60322AC');
        $this->addSql('DROP INDEX IDX_C3BA480714041B84 ON role_menu');
        $this->addSql('DROP INDEX `PRIMARY` ON role_menu');
        $this->addSql('ALTER TABLE role_menu DROP id, DROP permission, CHANGE menu_id menu_id INT NOT NULL');
        $this->addSql('ALTER TABLE role_menu ADD CONSTRAINT FK_C3BA4807CCD7E912 FOREIGN KEY (menu_id) REFERENCES menu (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE role_menu ADD CONSTRAINT FK_C3BA4807D60322AC FOREIGN KEY (role_id) REFERENCES role (id) ON DELETE CASCADE');
        $this->addSql('CREATE INDEX IDX_C3BA4807CCD7E912 ON role_menu (menu_id)');
        $this->addSql('ALTER TABLE role_menu ADD PRIMARY KEY (role_id, menu_id)');
    }
}