<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230214093514 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        // Insert Data into role Table
        $this->addSql("
            INSERT INTO `role` (`id`, `label`) VALUES
            (1, 'Super Administrator'),
            (2, 'Technical Administrator'),
            (3, 'Functional Administrator'),
            (4, 'Reader'),
            (5, 'Operations'),
            (6, 'Webmaster'),
            (7, 'Local Technical Administrator');
        ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
