<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231101093420 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE widget_feature_attribute ADD language_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE widget_feature_attribute ADD CONSTRAINT FK_CF102C9882F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)');
        $this->addSql('CREATE INDEX IDX_CF102C9882F1BAF4 ON widget_feature_attribute (language_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE widget_feature_attribute DROP FOREIGN KEY FK_CF102C9882F1BAF4');
        $this->addSql('DROP INDEX IDX_CF102C9882F1BAF4 ON widget_feature_attribute');
        $this->addSql('ALTER TABLE widget_feature_attribute DROP language_id');
    }
}
