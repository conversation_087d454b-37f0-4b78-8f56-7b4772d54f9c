<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240408120419 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("UPDATE language SET label = 'Francais' WHERE id = 1");
        $this->addSql("UPDATE language SET label = 'Tcheque' WHERE id = 7");
        $this->addSql("UPDATE language SET label = 'Hindi' WHERE id = 14");
        $this->addSql("UPDATE language SET label = 'Indonesien' WHERE id = 17");
        $this->addSql("UPDATE language SET label = 'Georgien' WHERE id = 20");
        $this->addSql("UPDATE language SET label = 'Coreen' WHERE id = 21");
        $this->addSql("UPDATE language SET label = 'Neerlandais' WHERE id = 26");
        $this->addSql("UPDATE language SET label = 'Norvegien' WHERE id = 27");
        $this->addSql("UPDATE language SET label = 'Slovene' WHERE id = 33");
        $this->addSql("UPDATE language SET label = 'Suedois' WHERE id = 35");
        $this->addSql("UPDATE language SET label = 'Thai' WHERE id = 36");
        $this->addSql("UPDATE language SET label = 'Hebreu' WHERE id = 45");
        $this->addSql("UPDATE language SET label = 'Hindi' WHERE id = 46");
        $this->addSql("UPDATE language SET label = 'Georgien' WHERE id = 48");
        $this->addSql("UPDATE language SET label = 'Coreen' WHERE id = 49");

    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
