<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250506211118 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
             SELECT id INTO @ACCESS_MENU_ID FROM `menu` WHERE label = 'menu_admin';
     
             INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`) 
             VALUES (NULL, @ACCESS_MENU_ID, 'EV Routing','fas fa-route', 'ev_routing_index', NULL, NULL);
   
             SELECT id INTO @EV_Routing_MENU_ID FROM `menu` WHERE label = 'EV Routing';
   
             SELECT id INTO @SUPER_ADMIN_ID FROM `role` WHERE label = 'Super Administrator';
   
              INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`) 
             VALUES (NULL, @SUPER_ADMIN_ID, @EV_Routing_MENU_ID, 'W');
   
           ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql("
            SELECT id INTO @EV_ROUTING_MENU_ID FROM `menu` WHERE label = 'EV Routing';

            DELETE FROM `role_menu` WHERE menu_id = @EV_ROUTING_MENU_ID;

            DELETE FROM `menu` WHERE id = @EV_ROUTING_MENU_ID;
        ");
    }
}
