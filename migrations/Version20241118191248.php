<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241118191248 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
      
        $this->addSql('ALTER TABLE menu DROP INDEX UNIQ_7D053A9360E4B879, ADD INDEX IDX_7D053A9360E4B879 (feature_id)');
        $this->addSql('ALTER TABLE menu DROP FOREIGN KEY FK_7D053A93B3750AF4');
        $this->addSql('ALTER TABLE menu ADD CONSTRAINT FK_7D053A93727ACA70 FOREIGN KEY (parent_id) REFERENCES menu (id)');
    }

    public function down(Schema $schema): void
    {

    }
}
