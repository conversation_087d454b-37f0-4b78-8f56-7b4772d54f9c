<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240626110206 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE access_log ADD profile_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE access_log ADD CONSTRAINT FK_EF7F3510CCFA12B8 FOREIGN KEY (profile_id) REFERENCES profile (id)');
        $this->addSql('CREATE INDEX IDX_EF7F3510CCFA12B8 ON access_log (profile_id)');
       
        $this->addSql('SELECT id INTO @PARENT_ID FROM `menu` WHERE label = "menu_admin"');
        $this->addSql('INSERT INTO `menu` (`parent_id`, `label`, `icon_class`, `route_name`) VALUES (@PARENT_ID, "access_log.menu", "", "access_log_index")');

        
       
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE access_log DROP FOREIGN KEY FK_EF7F3510CCFA12B8');
        $this->addSql('DROP INDEX IDX_EF7F3510CCFA12B8 ON access_log');
        $this->addSql('ALTER TABLE access_log DROP profile_id');
        $this->addSql('DELETE FROM menu WHERE  route_name = "access_log_index"');
    }
}
