<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240905153102 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE channel (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE media_directory ADD channel_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE media_directory ADD CONSTRAINT FK_6318F75472F5A1AA FOREIGN KEY (channel_id) REFERENCES channel (id)');
        $this->addSql('CREATE INDEX IDX_6318F75472F5A1AA ON media_directory (channel_id)');
        $this->addSql('ALTER TABLE media_directory ADD name VARCHAR(255) DEFAULT NULL');

        $this->addSql('CREATE TABLE channel_type (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE channel ADD channel_type_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE channel ADD CONSTRAINT FK_A2F98E47720FB392 FOREIGN KEY (channel_type_id) REFERENCES channel_type (id)');
        $this->addSql('CREATE INDEX IDX_A2F98E47720FB392 ON channel (channel_type_id)');

        $this->addSql("
        INSERT INTO channel (`name`, `description`) VALUES ('APP', 'APP channel'),('WEB', 'WEB channel'),('GLOBAL', 'GLOBAL channel'),('FLEET', 'FLEET channel');
        INSERT INTO channel_type (`name`, `description`) VALUES ('GLOBAL', 'Global type'),('BRAND', 'Brand type');
        SELECT id INTO @GLOBAL_ID FROM channel_type WHERE channel_type.name = 'GLOBAL';
        SELECT id INTO @BRAND_ID FROM channel_type WHERE channel_type.name = 'BRAND';
        UPDATE channel SET channel_type_id = @GLOBAL_ID WHERE channel.name IN ('GLOBAL', 'FLEET');
        UPDATE channel SET channel_type_id = @BRAND_ID WHERE channel.name IN ('APP', 'WEB');
        UPDATE media_directory SET label = 'APP', media_directory.path = 'APP' WHERE parent_directory_id IS NULL AND label = 'Racine' AND media_directory.path = 'Racine';
        UPDATE media_directory SET media_directory.path =  REPLACE(media_directory.path, 'Racine', 'APP') WHERE parent_directory_id IS NOT NULL;
        UPDATE media_directory md
        JOIN channel ch ON ch.name = TRIM(SUBSTRING_INDEX(md.path,'>',1))
        SET md.channel_id = ch.id;
        SELECT id INTO @CHANNEL_WEB_ID FROM channel WHERE name = 'WEB';
        SELECT id INTO @CHANNEL_GLOBAL_ID FROM channel WHERE name = 'GLOBAL';
        SELECT id INTO @CHANNEL_FLEET_ID FROM channel WHERE name = 'FLEET';
        INSERT INTO `media_directory` (`label`, `path`, `read_only`, `channel_id`) VALUES ('WEB', 'WEB', '1', @CHANNEL_WEB_ID),('GLOBAL', 'GLOBAL', '1', @CHANNEL_GLOBAL_ID), ('FLEET', 'FLEET', '1', @CHANNEL_FLEET_ID);
        SELECT id INTO @WEB_ID FROM media_directory WHERE media_directory.path = 'WEB' AND label = 'WEB';
        SELECT id INTO @APP_ID FROM media_directory WHERE media_directory.path = 'APP' AND label = 'APP';
        INSERT INTO media_directory (`site_id`,`parent_directory_id` ,`label`, `path`, `read_only`, `brand`, `channel_id`)
        SELECT md.site_id,  IF(md.parent_directory_id= @APP_ID, @WEB_ID, md.parent_directory_id), md.label, REPLACE(md.path, 'APP', 'WEB'), md.read_only, md.brand, @CHANNEL_WEB_ID
        FROM media_directory md
        WHERE md.parent_directory_id IS NOT null;
        UPDATE media_directory md
        JOIN media_directory m2 ON m2.channel_id = @CHANNEL_WEB_ID AND m2.brand = md.brand AND m2.label = TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(md.path,'>',-2),'>',1))
        SET md.parent_directory_id = m2.id
        WHERE md.channel_id = @CHANNEL_WEB_ID;
        UPDATE media_directory md
        LEFT JOIN site s ON s.id = md.site_id
        LEFT JOIN country c ON c.id = s.country_id
        LEFT JOIN brand b ON  b.id = s.brand_id OR (`md`.`brand` = b.code AND md.site_id IS NULL)
        SET md.name =  (
            CASE 
                WHEN md.site_id IS NOT NULL && md.label = c.code THEN c.name
                WHEN  md.site_id IS NULL AND md.brand IS NULL  THEN md.label
                WHEN  md.site_id IS NULL THEN b.name
                ELSE md.label
            END);
    ");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
