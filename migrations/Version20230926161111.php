<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230926161111 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE user_preference (id INT AUTO_INCREMENT NOT NULL, login_profile_id INT DEFAULT NULL, login_language_id INT DEFAULT NULL, username VARCHAR(255) NOT NULL, INDEX IDX_FA0E76BF14D8A01 (login_profile_id), INDEX IDX_FA0E76BF30810644 (login_language_id), UNIQUE INDEX username_unique_idx (username), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE user_preference ADD CONSTRAINT FK_FA0E76BF14D8A01 FOREIGN KEY (login_profile_id) REFERENCES profile (id)');
        $this->addSql('ALTER TABLE user_preference ADD CONSTRAINT FK_FA0E76BF30810644 FOREIGN KEY (login_language_id) REFERENCES language (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user_preference DROP FOREIGN KEY FK_FA0E76BF14D8A01');
        $this->addSql('ALTER TABLE user_preference DROP FOREIGN KEY FK_FA0E76BF30810644');
        $this->addSql('DROP TABLE user_preference');
    }
}
