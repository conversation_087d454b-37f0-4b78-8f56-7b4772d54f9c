<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230901084760 extends AbstractMigration
{
    public function getDescription(): string
    {
        return "create sites and profiles";
    }

    public function up(Schema $schema): void
    {
    //insert sites
    $this->addSql("INSERT INTO `site` (`id`, `brand_id`, `prefered_language_id`, `label`, `timezone`, `ldap_code`, `domtom`, `civilites_customer_at_active`, `reversed_name_order`, `distance_unit`, `consumption_unit`, `cost`, `volume_unit`, `date_format`, `hour_format`, `country_id`) VALUES
    (1, 1,  1,  'FRANCE AC',    'Europe/Paris', 'ACFR', 1,  1,  0,  'MILES',    'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    76),
    (2, 2,  1,  'FRANCE AP',    'Europe/Paris', 'APFR', 1,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    76),
    (3, 3,  1,  'FRANCE DS',    'Europe/Paris', 'DS_FR_ESP',    1,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    76),
    (4, 1,  2,  'ALLEMAGNE AC', 'Europe/Berlin',    'ACDE', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    83),
    (5, 2,  2,  'ESPAGNE AP',   'Europe/Madrid',    'APES', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    212),
    (6, 3,  1,  'ESPAGNE DS',   'Europe/Madrid',    'DS_ES_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    212),
    (7, 1,  1,  'SUISSE AC',    'Europe/Zurich',    'ACCH', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    219),
    (8, 2,  1,  'SUISSE AP',    'Europe/Zurich',    'APCH', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    219),
    (9, 3,  1,  'SUISSE DS',    'Europe/Zurich',    'DS_CH_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    219),
    (10,    6,  1,  'ADMINISTRATION',   'UTC',  NULL,   0,  1,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   152),
    (11,    NULL,   1,  'ADMINISTRATION CENTRAL',   'UTC',  '', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    NULL),
    (29,    1,  1,  'PORTUGAL AC',  'Europe/Lisbon',    'ACPT', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    180),
    (30,    4,  1,  'FRANCE OP',    'Europe/Paris', 'OP_FR_ESP',    1,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    76),
    (31,    1,  1,  'HONGRIE AC',   'Europe/Budapest',  'ACHU', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    101),
    (32,    1,  1,  'MAROC AC', 'Africa/Casablanca',    'ACMA', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    152),
    (33,    5,  2,  'UNITED KINGDOM VX',    'Europe/London',    'VX_GB_ESP',    0,  1,  0,  'MILES',    'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    238),
    (34,    2,  1,  'BELGIQUE AP',  'Europe/Brussels',  'APBE', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    22),
    (35,    2,  1,  'LUXEMBOURG AP',    'Europe/Luxembourg',    'APLU', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    131),
    (36,    2,  2,  'ARGENTINE AP', 'America/Argentina/Buenos Aires',   'APAR', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    11),
    (37,    2,  2,  'BRESIL AP',    'America/Sao Paulo',    'APBR', 0,  1,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    32),
    (38,    2,  2,  'ALLEMAGNE AP', 'Europe/Berlin',    'APDE', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    83),
    (39,    2,  2,  'PAYS-BAS AP',  'Europe/Amsterdam', 'APNL', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    158),
    (40,    2,  1,  'ITALIE AP',    'Europe/Rome',  'APIT', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    110),
    (41,    2,  2,  'GRANDE-BRETAGNE AP',   'Europe/London',    'APGB', 0,  1,  0,  'MILES',    'MPG',  'L',    'G',    'JJ/MM/AAAA',   '0..24',    238),
    (42,    2,  1,  'PORTUGAL AP',  'Europe/Lisbon',    'APPT', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    180),
    (43,    2,  2,  'AUTRICHE AP',  'Europe/Vienna',    'APAT', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    15),
    (44,    2,  2,  'IRAN AP',  'Africa/Abidjan',   'APIR', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    105),
    (45,    2,  2,  'POLOGNE AP',   'Africa/Accra', 'APPL', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    179),
    (46,    2,  2,  'DANEMARK AP',  'Africa/Accra', 'APDK', 0,  0,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    61),
    (47,    2,  2,  'NORWAY AP',    'Europe/Oslo',  'APNO', 0,  0,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    168),
    (48,    2,  2,  'SUEDE AP', 'Europe/Stockholm', 'APSE', 0,  0,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    218),
    (49,    2,  2,  'SLOVAQUIE AP', 'Europe/Bratislava',    'APSK', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    205),
    (50,    2,  2,  'REPUBLIQUE TCHEQUE AP',    'Europe/Prague',    'APCZ', 0,  0,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    60),
    (51,    2,  1,  'ALGERIE AP',   'Africa/Algiers',   'APDZ', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    4),
    (52,    2,  2,  'JAPON AP', 'Asia/Tokyo',   'APJP', 0,  1,  1,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    112),
    (53,    2,  2,  'TURQUIE AP',   'Europe/Istanbul',  'APTR', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    231),
    (54,    2,  1,  'CROATIE AP',   'Europe/Zagreb',    'APHR', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    56),
    (55,    2,  2,  'RUSSIE AP',    'Europe/Moscow',    'APRU', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    185),
    (56,    2,  1,  'MAROC AP', 'Africa/Casablanca',    'APMA', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    152),
    (57,    2,  1,  'HONGRIE AP',   'Europe/Budapest',  'APHU', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    101),
    (58,    2,  2,  'AUSTRALIE AP', 'Australia/Melbourne',  'APAU', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    14),
    (59,    2,  2,  'UKRAINE AP',   'Europe/Kiev',  'APUA', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    236),
    (60,    2,  2,  'EMIRATS ARABES UNIS AP',   'Asia/Dubai',   'APAE', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    237),
    (61,    2,  2,  'GRECE AP', 'Europe/Athens',    'APGR', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    86),
    (62,    2,  2,  'MEXIQUE AP',   'America/Mexico City',  'APMX', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    145),
    (63,    2,  2,  'COLOMBIE AP',  'America/Bogota',   'APCO', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    49),
    (64,    2,  2,  'SOUTH AFRICA AP',  'Africa/Johannesburg',  'APZA', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    209),
    (65,    2,  2,  'ROUMANIE AP',  'Europe/Bucharest', 'APRO', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    184),
    (66,    2,  2,  'COREE AP', 'Asia/Seoul',   'APKR', 0,  1,  1,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    119),
    (67,    1,  1,  'LUXEMBOURG AC',    'Europe/Luxembourg',    'ACLU', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    131),
    (68,    1,  2,  'ARGENTINE AC', 'America/Argentina/Buenos Aires',   'ACAR', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    11),
    (69,    1,  2,  'BRESIL AC',    'America/Sao Paulo',    'ACBR', 0,  1,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    32),
    (70,    1,  2,  'PAYS-BAS AC',  'Europe/Amsterdam', 'ACNL', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    158),
    (71,    1,  2,  'BELGIQUE AC',  'Europe/Brussels',  'ACBE', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    22),
    (72,    1,  1,  'ESPAGNE AC',   'Europe/Madrid',    'ACES', 0,  1,  1,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    212),
    (73,    1,  3,  'ITALIE AC',    'Europe/Rome',  'ACIT', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    110),
    (74,    1,  2,  'GRANDE-BRETAGNE AC',   'Europe/London',    'ACGB', 0,  1,  0,  'MILES',    'MPG',  'L',    'G',    'JJ/MM/AAAA',   '0..24',    238),
    (75,    1,  2,  'AUTRICHE AC',  'Europe/Vienna',    'ACAT', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    15),
    (76,    1,  2,  'POLOGNE AC',   'Africa/Abidjan',   'ACPL', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    179),
    (77,    1,  2,  'REPUBLIQUE TCHEQUE AC',    'Europe/Prague',    'ACCZ', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    60),
    (78,    1,  2,  'IRAN AC',  'Asia/Tehran',  'ACIR', 0,  1,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    105),
    (79,    1,  1,  'EMIRATS ARABES UNIS AC',   'Africa/Abidjan',   'ACAE', 0,  1,  0,  'MILES',    'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    237),
    (80,    1,  2,  'DANEMARK AC',  'Africa/Accra', 'ACDK', 0,  0,  1,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    61),
    (81,    1,  2,  'SUEDE AC', 'Europe/Stockholm', 'ACSE', 0,  0,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    218),
    (82,    1,  2,  'NORWAY AC',    'Europe/Oslo',  'ACNO', 0,  0,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    168),
    (83,    1,  2,  'SLOVAQUIE AC', 'Europe/Bratislava',    'ACSK', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    205),
    (84,    1,  2,  'JAPON AC', 'Asia/Tokyo',   'ACJP', 0,  1,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    112),
    (85,    1,  1,  'CROATIE AC',   'Europe/Zagreb',    'ACHR', 0,  1,  1,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    56),
    (86,    1,  2,  'TURQUIE AC',   'Europe/Istanbul',  'ACTR', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    231),
    (87,    1,  2,  'RUSSIE AC',    'Europe/Moscow',    'ACRU', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    185),
    (88,    1,  1,  'TUNISIE AC',   'Africa/Tunis', 'ACTN', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    230),
    (89,    1,  1,  'ALGERIE AC',   'Africa/Algiers',   'ACDZ', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    4),
    (90,    1,  2,  'IRLANDE AC',   'Europe/Dublin',    'ACIE', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    107),
    (91,    1,  2,  'ISRAEL AC',    'Asia/Jerusalem',   'ACIL', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    109),
    (92,    1,  2,  'GRECE AC', 'Europe/Athens',    'ACGR', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    86),
    (93,    1,  2,  'COLOMBIE AC',  'America/Bogota',   'ACCO', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    49),
    (94,    1,  2,  'CHILI AC', 'America/Santiago', 'ACCL', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    45),
    (95,    1,  1,  'MARTINIQUE AC',    'America/La Paz',   'ACMQ', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    141),
    (96,    1,  2,  'URUGUAY AC',   'America/Montevideo',   'ACUY', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    241),
    (97,    1,  2,  'ROUMANIE AC',  'Europe/Bucharest', 'ACRO', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    184),
    (98,    1,  2,  'SLOVENIE AC',  'Europe/Ljubljana', 'ACSI', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    206),
    (99,    1,  2,  'ISLANDE AC',   'Europe/London',    'ACIS', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    102),
    (100,   1,  2,  'INDE AC',  'Asia/Kolkata', 'ACIN', 0,  1,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    103),
    (101,   3,  1,  'LUXEMBOURG DS',    'Europe/Luxembourg',    'DS_LU_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    131),
    (102,   3,  2,  'ARGENTINE DS', 'America/Argentina/Buenos Aires',   'DS_AR_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    11),
    (103,   3,  2,  'BRESIL DS',    'America/Sao Paulo',    'DS_BR_ESP',    0,  1,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    32),
    (104,   3,  2,  'ALLEMAGNE DS', 'Europe/Berlin',    'DS_DE_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    83),
    (105,   3,  2,  'PAYS-BAS DS',  'Europe/Amsterdam', 'DS_NL_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    158),
    (106,   3,  1,  'BELGIQUE DS',  'Europe/Brussels',  'DS_BE_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    22),
    (107,   3,  1,  'ITALIE DS',    'Europe/Rome',  'DS_IT_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    110),
    (108,   3,  2,  'GRANDE-BRETAGNE DS',   'Europe/London',    'DS_GB_ESP',    0,  1,  0,  'MILES',    'MPG',  'L',    'G',    'JJ/MM/AAAA',   '0..24',    238),
    (109,   3,  1,  'PORTUGAL DS',  'Europe/Lisbon',    'DS_PT_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    180),
    (110,   3,  2,  'AUTRICHE DS',  'Europe/Vienna',    'DS_AT_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    15),
    (111,   3,  2,  'POLOGNE DS',   'Africa/Accra', 'DS_PL_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    179),
    (112,   3,  2,  'REPUBLIQUE TCHEQUE DS',    'Europe/Prague',    'DS_CZ_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    60),
    (113,   3,  2,  'DANEMARK DS',  'Africa/Accra', 'DS_DK_ESP',    0,  0,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    61),
    (114,   3,  2,  'SUEDE DS', 'Europe/Stockholm', 'DS_SE_ESP',    0,  0,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    218),
    (115,   3,  2,  'NORWAY DS',    'Europe/Oslo',  'DS_NO_ESP',    0,  0,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    168),
    (116,   3,  2,  'JAPON DS', 'Asia/Tokyo',   'DS_JP_ESP',    0,  1,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    112),
    (117,   3,  2,  'RUSSIE DS',    'Europe/Moscow',    'DS_RU_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    185),
    (118,   3,  2,  'CHILI DS', 'America/Santiago', 'DS_CL_ESP',    0,  1,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    45),
    (119,   3,  1,  'MAROC DS', 'Africa/Casablanca',    'DS_MA_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    152),
    (120,   3,  2,  'IRAN DS',  'Asia/Tehran',  'DS_IR_ESP',    0,  1,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    105),
    (121,   3,  1,  'TUNISIE DS',   'Africa/Tunis', 'DS_TN_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    230),
    (122,   3,  2,  'ISRAEL DS',    'Asia/Jerusalem',   'DS_IL_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    109),
    (123,   3,  2,  'TURQUIE DS',   'Europe/Istanbul',  'DS_TR_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    231),
    (124,   3,  2,  'GRECE DS', 'Europe/Athens',    'DS_GR_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    86),
    (125,   3,  1,  'GUYANE FRANCAISE DS',  'America/Cayenne',  'DS_GF_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    77),
    (126,   3,  2,  'COLOMBIE DS',  'America/Bogota',   'DS_CO_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    49),
    (127,   3,  1,  'MARTINIQUE DS',    'America/La Paz',   'DS_MQ_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    141),
    (128,   3,  1,  'GUADELOUPE DS',    'America/La Paz',   'DS_GP_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    89),
    (129,   3,  2,  'PALESTINE DS', 'Asia/Jerusalem',   'DS_PS_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    172),
    (130,   3,  2,  'SINGAPOUR DS', 'Asia/Singapore',   'DS_SG_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    203),
    (131,   3,  2,  'BRUNEI DS',    'Asia/Brunei',  'DS_BN_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    34),
    (132,   3,  2,  'MALAISIE DS',  'Asia/Kuala Lumpur',    'DS_MY_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    136),
    (133,   3,  2,  'CROATIE DS',   'Europe/Zagreb',    'DS_HR_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    56),
    (134,   3,  1,  'LA REUNION DS',    'Africa/Abidjan',   'DS_RE_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    183),
    (135,   3,  2,  'COREE DS', 'Asia/Seoul',   'DS_KR_ESP',    0,  1,  1,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    119),
    (136,   3,  2,  'FINLANDE DS',  'Europe/Helsinki',  'DS_FI_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    75),
    (137,   3,  2,  'HONGRIE DS',   'Europe/Budapest',  'DS_HU_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    101),
    (138,   3,  2,  'ISLANDE DS',   'Europe/London',    'DS_IS_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    102),
    (139,   3,  2,  'ROUMANIE DS',  'Europe/Bucharest', 'DS_RO_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    184),
    (140,   3,  2,  'SLOVENIE DS',  'Europe/Ljubljana', 'DS_SI_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    206),
    (141,   3,  2,  'SLOVAKIA DS',  'Europe/Bratislava',    'DS_SK_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    205),
    (142,   4,  2,  'AUSTRIA OP',   'Europe/Vienna',    'OP_AT_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    15),
    (143,   4,  1,  'BELGIUM OP',   'Europe/Brussels',  'OP_BE_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    22),
    (144,   4,  2,  'SWITZERLAND OP',   'Europe/Zurich',    'OP_CH_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    219),
    (145,   4,  1,  'ALLEMAGNE OP', 'Europe/Berlin',    'OP_DE_ESP',    0,  1,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    83),
    (146,   4,  2,  'DENMARK OP',   'Europe/Copenhagen',    'OP_DK_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    61),
    (147,   4,  2,  'SPAIN OP', 'Europe/Madrid',    'OP_ES_ESP',    0,  1,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   '0..24',    212),
    (148,   4,  2,  'FINLAND OP',   'Europe/Helsinki',  'OP_FI_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    75),
    (149,   4,  2,  'GREECE OP',    'Europe/Athens',    'OP_GR_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    86),
    (150,   4,  2,  'IRELAND OP',   'Europe/Dublin',    'OP_IE_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    107),
    (151,   4,  2,  'ITALY OP', 'Europe/Rome',  'OP_IT_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    110),
    (152,   4,  1,  'LUXEMBOURG OP',    'Europe/Luxembourg',    'OP_LU_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    131),
    (153,   4,  2,  'NETHERLANDS OP',   'Europe/Amsterdam', 'OP_NL_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    158),
    (154,   4,  2,  'NORWAY OP',    'Europe/Oslo',  'OP_NO_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    168),
    (155,   4,  2,  'POLAND OP',    'Europe/Warsaw',    'OP_PL_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    179),
    (156,   4,  2,  'PORTUGAL OP',  'Europe/Lisbon',    'OP_PT_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    180),
    (157,   4,  2,  'SWEDEN OP',    'Europe/Stockholm', 'OP_SE_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    218),
    (158,   4,  2,  'TURKEY OP',    'Europe/Istanbul',  'OP_TR_ESP',    0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    231),
    (159,   4,  2,  'GRANDE BRETAGNE OP',   'Europe/London',    'GB',   0,  1,  0,  'MILES',    'MPG',  'L',    'G',    'JJ-MM-AAAA',   '0..24',    238),
    (160,   4,  2,  'MALTA OP', 'Europe/Malta', 'OPMT', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    139),
    (161,   4,  2,  'BULGARIE OP',  'Europe/Prague',    'OPBG', 0,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    35),
    (162,   1,  1,  'TOGO CITROEN', 'Africa/Lome',  'Togo_LDAP',    1,  1,  0,  'KM',   'L/100',    'L',    'L',    'JJ/MM/AAAA',   '0..24',    233),
    (165,   18, 2,  'France FT',    'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   76),
    (166,   19, 2,  'France FO',    'UTC',  NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   76),
    (167,   20, 2,  'France AH',    'UTC',  NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   76),
    (168,   21, 2,  'France AR',    'UTC',  NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   NULL,   76),
    (169,   22, 2,  'France CY',    'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   76),
    (170,   23, 2,  'France DG',    'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   76),
    (171,   24, 1,  'France JE',    'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   76),
    (172,   25, 2,  'France LA',    'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   76),
    (173,   26, 2,  'France RM',    'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   76),
    (174,   27, 2,  'France MA',    'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   76),
    (177,   18, 2,  'Italy FT', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   110),
    (178,   19, 2,  'Italy FO', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   110),
    (179,   20, 2,  'Italy AH', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   110),
    (180,   21, 2,  'Italy AR', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   110),
    (181,   22, 2,  'Italy CY', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   110),
    (182,   23, 2,  'Italy DG', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   110),
    (183,   24, 2,  'Italy JE', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   110),
    (184,   25, 2,  'Italy LA', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   110),
    (185,   26, 2,  'Italy RM', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   110),
    (186,   27, 2,  'Italy MA', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   110),
    (189,   18, 2,  'Spain FT', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   212),
    (190,   19, 2,  'Spain FO', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   212),
    (191,   20, 2,  'Spain AH', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   212),
    (192,   21, 2,  'Spain AR', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   212),
    (193,   22, 2,  'Spain CY', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   212),
    (194,   23, 2,  'Spain DG', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   212),
    (195,   24, 2,  'Spain JE', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   212),
    (196,   25, 2,  'Spain LA', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   212),
    (197,   26, 2,  'Spain RM', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   212),
    (198,   27, 2,  'Spain MA', 'UTC',  NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   212),
    (199,   18, 2,  'Albania',  'Europe/Rome',  'TEST', 0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   3),
    (200,   2,  2,  'MALAYSIA AP',  'Asia/Kuala_Lumpur',    'MY',   0,  0,  0,  'KM',   'KM/L', 'L',    'L',    'JJ/MM/AAAA',   'AM/PM',    136),
    (201,   24, 2,  'JEEP AT',  'Europe/Paris', NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   'JJ/MM/AAAA',   '0..24',    15),
    (202,   24, 2,  'Jeep Albania', 'Europe/Tirane',    NULL,   0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   3),
    (203,   1,  2,  'Peugeot Al',   'Europe/Tirane',    'PGAL', 0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   3),
    (204,   21, 2,  'AUSTRIA AR',   'Europe/Vienna',    'LPA',  0,  0,  0,  'KM',   NULL,   NULL,   NULL,   NULL,   NULL,   15);");
    // insert profiles       
    $this->addSql("INSERT INTO `profile` (`id`, `site_id`, `role_id`, `profile_admin`, `brand_id`) VALUES
        (1, 10, 1,  0,  NULL),
        (2, 11, 2,  0,  1),
        (3, 11, 3,  0,  1),
        (4, 11, 2,  0,  2),
        (5, 11, 3,  0,  2),
        (6, 11, 2,  0,  3),
        (7, 11, 3,  0,  3),
        (8, 11, 2,  0,  4),
        (9, 11, 3,  0,  4),
        (10,    11, 2,  0,  5),
        (11,    11, 3,  0,  5),
        (14,    11, 2,  0,  18),
        (15,    11, 3,  0,  18),
        (16,    11, 2,  0,  19),
        (17,    11, 3,  0,  19),
        (18,    11, 2,  0,  20),
        (19,    11, 3,  0,  20),
        (20,    11, 2,  0,  21),
        (21,    11, 3,  0,  21),
        (22,    11, 2,  0,  22),
        (23,    11, 3,  0,  22),
        (24,    11, 2,  0,  23),
        (25,    11, 3,  0,  23),
        (26,    11, 2,  0,  24),
        (27,    11, 3,  0,  24),
        (28,    11, 2,  0,  25),
        (29,    11, 3,  0,  25),
        (30,    11, 2,  0,  26),
        (31,    11, 3,  0,  26),
        (32,    11, 2,  0,  27),
        (33,    11, 3,  0,  27),
        (65,    1,  7,  0,  NULL),
        (66,    1,  5,  0,  NULL),
        (67,    1,  6,  0,  NULL),
        (68,    1,  7,  0,  NULL),
        (69,    2,  4,  0,  NULL),
        (70,    2,  5,  0,  NULL),
        (71,    2,  6,  0,  NULL),
        (72,    2,  7,  0,  NULL),
        (73,    3,  4,  0,  NULL),
        (74,    3,  5,  0,  NULL),
        (75,    3,  6,  0,  NULL),
        (76,    3,  7,  0,  NULL),
        (77,    4,  4,  0,  NULL),
        (78,    4,  5,  0,  NULL),
        (79,    4,  6,  0,  NULL),
        (80,    4,  7,  0,  NULL),
        (81,    5,  4,  0,  NULL),
        (82,    5,  5,  0,  NULL),
        (83,    5,  6,  0,  NULL),
        (84,    5,  7,  0,  NULL),
        (85,    6,  4,  0,  NULL),
        (86,    6,  5,  0,  NULL),
        (87,    6,  6,  0,  NULL),
        (88,    6,  7,  0,  NULL),
        (89,    7,  4,  0,  NULL),
        (90,    7,  5,  0,  NULL),
        (91,    7,  6,  0,  NULL),
        (92,    7,  7,  0,  NULL),
        (93,    8,  4,  0,  NULL),
        (94,    8,  5,  0,  NULL),
        (95,    8,  6,  0,  NULL),
        (96,    8,  7,  0,  NULL),
        (97,    9,  4,  0,  NULL),
        (98,    9,  5,  0,  NULL),
        (99,    9,  6,  0,  NULL),
        (100,   9,  7,  0,  NULL),
        (101,   29, 4,  0,  NULL),
        (102,   29, 5,  0,  NULL),
        (103,   29, 6,  0,  NULL),
        (104,   29, 7,  0,  NULL),
        (105,   30, 4,  0,  NULL),
        (106,   30, 5,  0,  NULL),
        (107,   30, 6,  0,  NULL),
        (108,   30, 7,  0,  NULL),
        (109,   31, 4,  0,  NULL),
        (110,   31, 5,  0,  NULL),
        (111,   31, 6,  0,  NULL),
        (112,   31, 7,  0,  NULL),
        (113,   32, 4,  0,  NULL),
        (114,   32, 5,  0,  NULL),
        (115,   32, 6,  0,  NULL),
        (116,   32, 7,  0,  NULL),
        (117,   33, 4,  0,  NULL),
        (118,   33, 5,  0,  NULL),
        (119,   33, 6,  0,  NULL),
        (120,   33, 7,  0,  NULL),
        (121,   34, 4,  0,  NULL),
        (122,   34, 5,  0,  NULL),
        (123,   34, 6,  0,  NULL),
        (124,   34, 7,  0,  NULL),
        (125,   35, 4,  0,  NULL),
        (126,   35, 5,  0,  NULL),
        (127,   35, 6,  0,  NULL),
        (128,   35, 7,  0,  NULL),
        (129,   36, 4,  0,  NULL),
        (130,   36, 5,  0,  NULL),
        (131,   36, 6,  0,  NULL),
        (132,   36, 7,  0,  NULL),
        (133,   37, 4,  0,  NULL),
        (134,   37, 5,  0,  NULL),
        (135,   37, 6,  0,  NULL),
        (136,   37, 7,  0,  NULL),
        (137,   38, 4,  0,  NULL),
        (138,   38, 5,  0,  NULL),
        (139,   38, 6,  0,  NULL),
        (140,   38, 7,  0,  NULL),
        (141,   39, 4,  0,  NULL),
        (142,   39, 5,  0,  NULL),
        (143,   39, 6,  0,  NULL),
        (144,   39, 7,  0,  NULL),
        (145,   40, 4,  0,  NULL),
        (146,   40, 5,  0,  NULL),
        (147,   40, 6,  0,  NULL),
        (148,   40, 7,  0,  NULL),
        (149,   41, 4,  0,  NULL),
        (150,   41, 5,  0,  NULL),
        (151,   41, 6,  0,  NULL),
        (152,   41, 7,  0,  NULL),
        (153,   42, 4,  0,  NULL),
        (154,   42, 5,  0,  NULL),
        (155,   42, 6,  0,  NULL),
        (156,   42, 7,  0,  NULL),
        (157,   43, 4,  0,  NULL),
        (158,   43, 5,  0,  NULL),
        (159,   43, 6,  0,  NULL),
        (160,   43, 7,  0,  NULL),
        (161,   44, 4,  0,  NULL),
        (162,   44, 5,  0,  NULL),
        (163,   44, 6,  0,  NULL),
        (164,   44, 7,  0,  NULL),
        (165,   45, 4,  0,  NULL),
        (166,   45, 5,  0,  NULL),
        (167,   45, 6,  0,  NULL),
        (168,   45, 7,  0,  NULL),
        (169,   46, 4,  0,  NULL),
        (170,   46, 5,  0,  NULL),
        (171,   46, 6,  0,  NULL),
        (172,   46, 7,  0,  NULL),
        (173,   47, 4,  0,  NULL),
        (174,   47, 5,  0,  NULL),
        (175,   47, 6,  0,  NULL),
        (176,   47, 7,  0,  NULL),
        (177,   48, 4,  0,  NULL),
        (178,   48, 5,  0,  NULL),
        (179,   48, 6,  0,  NULL),
        (180,   48, 7,  0,  NULL),
        (181,   49, 4,  0,  NULL),
        (182,   49, 5,  0,  NULL),
        (183,   49, 6,  0,  NULL),
        (184,   49, 7,  0,  NULL),
        (185,   50, 4,  0,  NULL),
        (186,   50, 5,  0,  NULL),
        (187,   50, 6,  0,  NULL),
        (188,   50, 7,  0,  NULL),
        (189,   51, 4,  0,  NULL),
        (190,   51, 5,  0,  NULL),
        (191,   51, 6,  0,  NULL),
        (192,   51, 7,  0,  NULL),
        (193,   52, 4,  0,  NULL),
        (194,   52, 5,  0,  NULL),
        (195,   52, 6,  0,  NULL),
        (196,   52, 7,  0,  NULL),
        (197,   53, 4,  0,  NULL),
        (198,   53, 5,  0,  NULL),
        (199,   53, 6,  0,  NULL),
        (200,   53, 7,  0,  NULL),
        (201,   54, 4,  0,  NULL),
        (202,   54, 5,  0,  NULL),
        (203,   54, 6,  0,  NULL),
        (204,   54, 7,  0,  NULL),
        (205,   55, 4,  0,  NULL),
        (206,   55, 5,  0,  NULL),
        (207,   55, 6,  0,  NULL),
        (208,   55, 7,  0,  NULL),
        (209,   56, 4,  0,  NULL),
        (210,   56, 5,  0,  NULL),
        (211,   56, 6,  0,  NULL),
        (212,   56, 7,  0,  NULL),
        (213,   57, 4,  0,  NULL),
        (214,   57, 5,  0,  NULL),
        (215,   57, 6,  0,  NULL),
        (216,   57, 7,  0,  NULL),
        (217,   58, 4,  0,  NULL),
        (218,   58, 5,  0,  NULL),
        (219,   58, 6,  0,  NULL),
        (220,   58, 7,  0,  NULL),
        (221,   59, 4,  0,  NULL),
        (222,   59, 5,  0,  NULL),
        (223,   59, 6,  0,  NULL),
        (224,   59, 7,  0,  NULL),
        (225,   60, 4,  0,  NULL),
        (226,   60, 5,  0,  NULL),
        (227,   60, 6,  0,  NULL),
        (228,   60, 7,  0,  NULL),
        (229,   61, 4,  0,  NULL),
        (230,   61, 5,  0,  NULL),
        (231,   61, 6,  0,  NULL),
        (232,   61, 7,  0,  NULL),
        (233,   62, 4,  0,  NULL),
        (234,   62, 5,  0,  NULL),
        (235,   62, 6,  0,  NULL),
        (236,   62, 7,  0,  NULL),
        (237,   63, 4,  0,  NULL),
        (238,   63, 5,  0,  NULL),
        (239,   63, 6,  0,  NULL),
        (240,   63, 7,  0,  NULL),
        (241,   64, 4,  0,  NULL),
        (242,   64, 5,  0,  NULL),
        (243,   64, 6,  0,  NULL),
        (244,   64, 7,  0,  NULL),
        (245,   65, 4,  0,  NULL),
        (246,   65, 5,  0,  NULL),
        (247,   65, 6,  0,  NULL),
        (248,   65, 7,  0,  NULL),
        (249,   66, 4,  0,  NULL),
        (250,   66, 5,  0,  NULL),
        (251,   66, 6,  0,  NULL),
        (252,   66, 7,  0,  NULL),
        (253,   67, 4,  0,  NULL),
        (254,   67, 5,  0,  NULL),
        (255,   67, 6,  0,  NULL),
        (256,   67, 7,  0,  NULL),
        (257,   68, 4,  0,  NULL),
        (258,   68, 5,  0,  NULL),
        (259,   68, 6,  0,  NULL),
        (260,   68, 7,  0,  NULL),
        (261,   69, 4,  0,  NULL),
        (262,   69, 5,  0,  NULL),
        (263,   69, 6,  0,  NULL),
        (264,   69, 7,  0,  NULL),
        (265,   70, 4,  0,  NULL),
        (266,   70, 5,  0,  NULL),
        (267,   70, 6,  0,  NULL),
        (268,   70, 7,  0,  NULL),
        (269,   71, 4,  0,  NULL),
        (270,   71, 5,  0,  NULL),
        (271,   71, 6,  0,  NULL),
        (272,   71, 7,  0,  NULL),
        (273,   72, 4,  0,  NULL),
        (274,   72, 5,  0,  NULL),
        (275,   72, 6,  0,  NULL),
        (276,   72, 7,  0,  NULL),
        (277,   73, 4,  0,  NULL),
        (278,   73, 5,  0,  NULL),
        (279,   73, 6,  0,  NULL),
        (280,   73, 7,  0,  NULL),
        (281,   74, 4,  0,  NULL),
        (282,   74, 5,  0,  NULL),
        (283,   74, 6,  0,  NULL),
        (284,   74, 7,  0,  NULL),
        (285,   75, 4,  0,  NULL),
        (286,   75, 5,  0,  NULL),
        (287,   75, 6,  0,  NULL),
        (288,   75, 7,  0,  NULL),
        (289,   76, 4,  0,  NULL),
        (290,   76, 5,  0,  NULL),
        (291,   76, 6,  0,  NULL),
        (292,   76, 7,  0,  NULL),
        (293,   77, 4,  0,  NULL),
        (294,   77, 5,  0,  NULL),
        (295,   77, 6,  0,  NULL),
        (296,   77, 7,  0,  NULL),
        (297,   78, 4,  0,  NULL),
        (298,   78, 5,  0,  NULL),
        (299,   78, 6,  0,  NULL),
        (300,   78, 7,  0,  NULL),
        (301,   79, 4,  0,  NULL),
        (302,   79, 5,  0,  NULL),
        (303,   79, 6,  0,  NULL),
        (304,   79, 7,  0,  NULL),
        (305,   80, 4,  0,  NULL),
        (306,   80, 5,  0,  NULL),
        (307,   80, 6,  0,  NULL),
        (308,   80, 7,  0,  NULL),
        (309,   81, 4,  0,  NULL),
        (310,   81, 5,  0,  NULL),
        (311,   81, 6,  0,  NULL),
        (312,   81, 7,  0,  NULL),
        (313,   82, 4,  0,  NULL),
        (314,   82, 5,  0,  NULL),
        (315,   82, 6,  0,  NULL),
        (316,   82, 7,  0,  NULL),
        (317,   83, 4,  0,  NULL),
        (318,   83, 5,  0,  NULL),
        (319,   83, 6,  0,  NULL),
        (320,   83, 7,  0,  NULL),
        (321,   84, 4,  0,  NULL),
        (322,   84, 5,  0,  NULL),
        (323,   84, 6,  0,  NULL),
        (324,   84, 7,  0,  NULL),
        (325,   85, 4,  0,  NULL),
        (326,   85, 5,  0,  NULL),
        (327,   85, 6,  0,  NULL),
        (328,   85, 7,  0,  NULL),
        (329,   86, 4,  0,  NULL),
        (330,   86, 5,  0,  NULL),
        (331,   86, 6,  0,  NULL),
        (332,   86, 7,  0,  NULL),
        (333,   87, 4,  0,  NULL),
        (334,   87, 5,  0,  NULL),
        (335,   87, 6,  0,  NULL),
        (336,   87, 7,  0,  NULL),
        (337,   88, 4,  0,  NULL),
        (338,   88, 5,  0,  NULL),
        (339,   88, 6,  0,  NULL),
        (340,   88, 7,  0,  NULL),
        (341,   89, 4,  0,  NULL),
        (342,   89, 5,  0,  NULL),
        (343,   89, 6,  0,  NULL),
        (344,   89, 7,  0,  NULL),
        (345,   90, 4,  0,  NULL),
        (346,   90, 5,  0,  NULL),
        (347,   90, 6,  0,  NULL),
        (348,   90, 7,  0,  NULL),
        (349,   91, 4,  0,  NULL),
        (350,   91, 5,  0,  NULL),
        (351,   91, 6,  0,  NULL),
        (352,   91, 7,  0,  NULL),
        (353,   92, 4,  0,  NULL),
        (354,   92, 5,  0,  NULL),
        (355,   92, 6,  0,  NULL),
        (356,   92, 7,  0,  NULL),
        (357,   93, 4,  0,  NULL),
        (358,   93, 5,  0,  NULL),
        (359,   93, 6,  0,  NULL),
        (360,   93, 7,  0,  NULL),
        (361,   94, 4,  0,  NULL),
        (362,   94, 5,  0,  NULL),
        (363,   94, 6,  0,  NULL),
        (364,   94, 7,  0,  NULL),
        (365,   95, 4,  0,  NULL),
        (366,   95, 5,  0,  NULL),
        (367,   95, 6,  0,  NULL),
        (368,   95, 7,  0,  NULL),
        (369,   96, 4,  0,  NULL),
        (370,   96, 5,  0,  NULL),
        (371,   96, 6,  0,  NULL),
        (372,   96, 7,  0,  NULL),
        (373,   97, 4,  0,  NULL),
        (374,   97, 5,  0,  NULL),
        (375,   97, 6,  0,  NULL),
        (376,   97, 7,  0,  NULL),
        (377,   98, 4,  0,  NULL),
        (378,   98, 5,  0,  NULL),
        (379,   98, 6,  0,  NULL),
        (380,   98, 7,  0,  NULL),
        (381,   99, 4,  0,  NULL),
        (382,   99, 5,  0,  NULL),
        (383,   99, 6,  0,  NULL),
        (384,   99, 7,  0,  NULL),
        (385,   100,    4,  0,  NULL),
        (386,   100,    5,  0,  NULL),
        (387,   100,    6,  0,  NULL),
        (388,   100,    7,  0,  NULL),
        (389,   101,    4,  0,  NULL),
        (390,   101,    5,  0,  NULL),
        (391,   101,    6,  0,  NULL),
        (392,   101,    7,  0,  NULL),
        (393,   102,    4,  0,  NULL),
        (394,   102,    5,  0,  NULL),
        (395,   102,    6,  0,  NULL),
        (396,   102,    7,  0,  NULL),
        (397,   103,    4,  0,  NULL),
        (398,   103,    5,  0,  NULL),
        (399,   103,    6,  0,  NULL),
        (400,   103,    7,  0,  NULL),
        (401,   104,    4,  0,  NULL),
        (402,   104,    5,  0,  NULL),
        (403,   104,    6,  0,  NULL),
        (404,   104,    7,  0,  NULL),
        (405,   105,    4,  0,  NULL),
        (406,   105,    5,  0,  NULL),
        (407,   105,    6,  0,  NULL),
        (408,   105,    7,  0,  NULL),
        (409,   106,    4,  0,  NULL),
        (410,   106,    5,  0,  NULL),
        (411,   106,    6,  0,  NULL),
        (412,   106,    7,  0,  NULL),
        (413,   107,    4,  0,  NULL),
        (414,   107,    5,  0,  NULL),
        (415,   107,    6,  0,  NULL),
        (416,   107,    7,  0,  NULL),
        (417,   108,    4,  0,  NULL),
        (418,   108,    5,  0,  NULL),
        (419,   108,    6,  0,  NULL),
        (420,   108,    7,  0,  NULL),
        (421,   109,    4,  0,  NULL),
        (422,   109,    5,  0,  NULL),
        (423,   109,    6,  0,  NULL),
        (424,   109,    7,  0,  NULL),
        (425,   110,    4,  0,  NULL),
        (426,   110,    5,  0,  NULL),
        (427,   110,    6,  0,  NULL),
        (428,   110,    7,  0,  NULL),
        (429,   111,    4,  0,  NULL),
        (430,   111,    5,  0,  NULL),
        (431,   111,    6,  0,  NULL),
        (432,   111,    7,  0,  NULL),
        (433,   112,    4,  0,  NULL),
        (434,   112,    5,  0,  NULL),
        (435,   112,    6,  0,  NULL),
        (436,   112,    7,  0,  NULL),
        (437,   113,    4,  0,  NULL),
        (438,   113,    5,  0,  NULL),
        (439,   113,    6,  0,  NULL),
        (440,   113,    7,  0,  NULL),
        (441,   114,    4,  0,  NULL),
        (442,   114,    5,  0,  NULL),
        (443,   114,    6,  0,  NULL),
        (444,   114,    7,  0,  NULL),
        (445,   115,    4,  0,  NULL),
        (446,   115,    5,  0,  NULL),
        (447,   115,    6,  0,  NULL),
        (448,   115,    7,  0,  NULL),
        (449,   116,    4,  0,  NULL),
        (450,   116,    5,  0,  NULL),
        (451,   116,    6,  0,  NULL),
        (452,   116,    7,  0,  NULL),
        (453,   117,    4,  0,  NULL),
        (454,   117,    5,  0,  NULL),
        (455,   117,    6,  0,  NULL),
        (456,   117,    7,  0,  NULL),
        (457,   118,    4,  0,  NULL),
        (458,   118,    5,  0,  NULL),
        (459,   118,    6,  0,  NULL),
        (460,   118,    7,  0,  NULL),
        (461,   119,    4,  0,  NULL),
        (462,   119,    5,  0,  NULL),
        (463,   119,    6,  0,  NULL),
        (464,   119,    7,  0,  NULL),
        (465,   120,    4,  0,  NULL),
        (466,   120,    5,  0,  NULL),
        (467,   120,    6,  0,  NULL),
        (468,   120,    7,  0,  NULL),
        (469,   121,    4,  0,  NULL),
        (470,   121,    5,  0,  NULL),
        (471,   121,    6,  0,  NULL),
        (472,   121,    7,  0,  NULL),
        (473,   122,    4,  0,  NULL),
        (474,   122,    5,  0,  NULL),
        (475,   122,    6,  0,  NULL),
        (476,   122,    7,  0,  NULL),
        (477,   123,    4,  0,  NULL),
        (478,   123,    5,  0,  NULL),
        (479,   123,    6,  0,  NULL),
        (480,   123,    7,  0,  NULL),
        (481,   124,    4,  0,  NULL),
        (482,   124,    5,  0,  NULL),
        (483,   124,    6,  0,  NULL),
        (484,   124,    7,  0,  NULL),
        (485,   125,    4,  0,  NULL),
        (486,   125,    5,  0,  NULL),
        (487,   125,    6,  0,  NULL),
        (488,   125,    7,  0,  NULL),
        (489,   126,    4,  0,  NULL),
        (490,   126,    5,  0,  NULL),
        (491,   126,    6,  0,  NULL),
        (492,   126,    7,  0,  NULL),
        (493,   127,    4,  0,  NULL),
        (494,   127,    5,  0,  NULL),
        (495,   127,    6,  0,  NULL),
        (496,   127,    7,  0,  NULL),
        (497,   128,    4,  0,  NULL),
        (498,   128,    5,  0,  NULL),
        (499,   128,    6,  0,  NULL),
        (500,   128,    7,  0,  NULL),
        (501,   129,    4,  0,  NULL),
        (502,   129,    5,  0,  NULL),
        (503,   129,    6,  0,  NULL),
        (504,   129,    7,  0,  NULL),
        (505,   130,    4,  0,  NULL),
        (506,   130,    5,  0,  NULL),
        (507,   130,    6,  0,  NULL),
        (508,   130,    7,  0,  NULL),
        (509,   131,    4,  0,  NULL),
        (510,   131,    5,  0,  NULL),
        (511,   131,    6,  0,  NULL),
        (512,   131,    7,  0,  NULL),
        (513,   132,    4,  0,  NULL),
        (514,   132,    5,  0,  NULL),
        (515,   132,    6,  0,  NULL),
        (516,   132,    7,  0,  NULL),
        (517,   133,    4,  0,  NULL),
        (518,   133,    5,  0,  NULL),
        (519,   133,    6,  0,  NULL),
        (520,   133,    7,  0,  NULL),
        (521,   134,    4,  0,  NULL),
        (522,   134,    5,  0,  NULL),
        (523,   134,    6,  0,  NULL),
        (524,   134,    7,  0,  NULL),
        (525,   135,    4,  0,  NULL),
        (526,   135,    5,  0,  NULL),
        (527,   135,    6,  0,  NULL),
        (528,   135,    7,  0,  NULL),
        (529,   136,    4,  0,  NULL),
        (530,   136,    5,  0,  NULL),
        (531,   136,    6,  0,  NULL),
        (532,   136,    7,  0,  NULL),
        (533,   137,    4,  0,  NULL),
        (534,   137,    5,  0,  NULL),
        (535,   137,    6,  0,  NULL),
        (536,   137,    7,  0,  NULL),
        (537,   138,    4,  0,  NULL),
        (538,   138,    5,  0,  NULL),
        (539,   138,    6,  0,  NULL),
        (540,   138,    7,  0,  NULL),
        (541,   139,    4,  0,  NULL),
        (542,   139,    5,  0,  NULL),
        (543,   139,    6,  0,  NULL),
        (544,   139,    7,  0,  NULL),
        (545,   140,    4,  0,  NULL),
        (546,   140,    5,  0,  NULL),
        (547,   140,    6,  0,  NULL),
        (548,   140,    7,  0,  NULL),
        (549,   141,    4,  0,  NULL),
        (550,   141,    5,  0,  NULL),
        (551,   141,    6,  0,  NULL),
        (552,   141,    7,  0,  NULL),
        (553,   142,    4,  0,  NULL),
        (554,   142,    5,  0,  NULL),
        (555,   142,    6,  0,  NULL),
        (556,   142,    7,  0,  NULL),
        (557,   143,    4,  0,  NULL),
        (558,   143,    5,  0,  NULL),
        (559,   143,    6,  0,  NULL),
        (560,   143,    7,  0,  NULL),
        (561,   144,    4,  0,  NULL),
        (562,   144,    5,  0,  NULL),
        (563,   144,    6,  0,  NULL),
        (564,   144,    7,  0,  NULL),
        (565,   145,    4,  0,  NULL),
        (566,   145,    5,  0,  NULL),
        (567,   145,    6,  0,  NULL),
        (568,   145,    7,  0,  NULL),
        (569,   146,    4,  0,  NULL),
        (570,   146,    5,  0,  NULL),
        (571,   146,    6,  0,  NULL),
        (572,   146,    7,  0,  NULL),
        (573,   147,    4,  0,  NULL),
        (574,   147,    5,  0,  NULL),
        (575,   147,    6,  0,  NULL),
        (576,   147,    7,  0,  NULL),
        (577,   148,    4,  0,  NULL),
        (578,   148,    5,  0,  NULL),
        (579,   148,    6,  0,  NULL),
        (580,   148,    7,  0,  NULL),
        (581,   149,    4,  0,  NULL),
        (582,   149,    5,  0,  NULL),
        (583,   149,    6,  0,  NULL),
        (584,   149,    7,  0,  NULL),
        (585,   150,    4,  0,  NULL),
        (586,   150,    5,  0,  NULL),
        (587,   150,    6,  0,  NULL),
        (588,   150,    7,  0,  NULL),
        (589,   151,    4,  0,  NULL),
        (590,   151,    5,  0,  NULL),
        (591,   151,    6,  0,  NULL),
        (592,   151,    7,  0,  NULL),
        (593,   152,    4,  0,  NULL),
        (594,   152,    5,  0,  NULL),
        (595,   152,    6,  0,  NULL),
        (596,   152,    7,  0,  NULL),
        (597,   153,    4,  0,  NULL),
        (598,   153,    5,  0,  NULL),
        (599,   153,    6,  0,  NULL),
        (600,   153,    7,  0,  NULL),
        (601,   154,    4,  0,  NULL),
        (602,   154,    5,  0,  NULL),
        (603,   154,    6,  0,  NULL),
        (604,   154,    7,  0,  NULL),
        (605,   155,    4,  0,  NULL),
        (606,   155,    5,  0,  NULL),
        (607,   155,    6,  0,  NULL),
        (608,   155,    7,  0,  NULL),
        (609,   156,    4,  0,  NULL),
        (610,   156,    5,  0,  NULL),
        (611,   156,    6,  0,  NULL),
        (612,   156,    7,  0,  NULL),
        (613,   157,    4,  0,  NULL),
        (614,   157,    5,  0,  NULL),
        (615,   157,    6,  0,  NULL),
        (616,   157,    7,  0,  NULL),
        (617,   158,    4,  0,  NULL),
        (618,   158,    5,  0,  NULL),
        (619,   158,    6,  0,  NULL),
        (620,   158,    7,  0,  NULL),
        (621,   159,    4,  0,  NULL),
        (622,   159,    5,  0,  NULL),
        (623,   159,    6,  0,  NULL),
        (624,   159,    7,  0,  NULL),
        (625,   160,    4,  0,  NULL),
        (626,   160,    5,  0,  NULL),
        (627,   160,    6,  0,  NULL),
        (628,   160,    7,  0,  NULL),
        (629,   161,    4,  0,  NULL),
        (630,   161,    5,  0,  NULL),
        (631,   161,    6,  0,  NULL),
        (632,   161,    7,  0,  NULL),
        (633,   162,    4,  0,  NULL),
        (634,   162,    5,  0,  NULL),
        (635,   162,    6,  0,  NULL),
        (636,   162,    7,  0,  NULL),
        (645,   165,    4,  0,  NULL),
        (646,   165,    5,  0,  NULL),
        (647,   165,    6,  0,  NULL),
        (648,   165,    7,  0,  NULL),
        (649,   166,    4,  0,  NULL),
        (650,   166,    5,  0,  NULL),
        (651,   166,    6,  0,  NULL),
        (652,   166,    7,  0,  NULL),
        (653,   167,    4,  0,  NULL),
        (654,   167,    5,  0,  NULL),
        (655,   167,    6,  0,  NULL),
        (656,   167,    7,  0,  NULL),
        (657,   168,    4,  0,  NULL),
        (658,   168,    5,  0,  NULL),
        (659,   168,    6,  0,  NULL),
        (660,   168,    7,  0,  NULL),
        (661,   169,    4,  0,  NULL),
        (662,   169,    5,  0,  NULL),
        (663,   169,    6,  0,  NULL),
        (664,   169,    7,  0,  NULL),
        (665,   170,    4,  0,  NULL),
        (666,   170,    5,  0,  NULL),
        (667,   170,    6,  0,  NULL),
        (668,   170,    7,  0,  NULL),
        (669,   171,    4,  0,  NULL),
        (670,   171,    5,  0,  NULL),
        (671,   171,    6,  0,  NULL),
        (672,   171,    7,  0,  NULL),
        (673,   172,    4,  0,  NULL),
        (674,   172,    5,  0,  NULL),
        (675,   172,    6,  0,  NULL),
        (676,   172,    7,  0,  NULL),
        (677,   173,    4,  0,  NULL),
        (678,   173,    5,  0,  NULL),
        (679,   173,    6,  0,  NULL),
        (680,   173,    7,  0,  NULL),
        (681,   174,    4,  0,  NULL),
        (682,   174,    5,  0,  NULL),
        (683,   174,    6,  0,  NULL),
        (684,   174,    7,  0,  NULL),
        (693,   177,    4,  0,  NULL),
        (694,   177,    5,  0,  NULL),
        (695,   177,    6,  0,  NULL),
        (696,   177,    7,  0,  NULL),
        (697,   178,    4,  0,  NULL),
        (698,   178,    5,  0,  NULL),
        (699,   178,    6,  0,  NULL),
        (700,   178,    7,  0,  NULL),
        (701,   179,    4,  0,  NULL),
        (702,   179,    5,  0,  NULL),
        (703,   179,    6,  0,  NULL),
        (704,   179,    7,  0,  NULL),
        (705,   180,    4,  0,  NULL),
        (706,   180,    5,  0,  NULL),
        (707,   180,    6,  0,  NULL),
        (708,   180,    7,  0,  NULL),
        (709,   181,    4,  0,  NULL),
        (710,   181,    5,  0,  NULL),
        (711,   181,    6,  0,  NULL),
        (712,   181,    7,  0,  NULL),
        (713,   182,    4,  0,  NULL),
        (714,   182,    5,  0,  NULL),
        (715,   182,    6,  0,  NULL),
        (716,   182,    7,  0,  NULL),
        (717,   183,    4,  0,  NULL),
        (718,   183,    5,  0,  NULL),
        (719,   183,    6,  0,  NULL),
        (720,   183,    7,  0,  NULL),
        (721,   184,    4,  0,  NULL),
        (722,   184,    5,  0,  NULL),
        (723,   184,    6,  0,  NULL),
        (724,   184,    7,  0,  NULL),
        (725,   185,    4,  0,  NULL),
        (726,   185,    5,  0,  NULL),
        (727,   185,    6,  0,  NULL),
        (728,   185,    7,  0,  NULL),
        (729,   186,    4,  0,  NULL),
        (730,   186,    5,  0,  NULL),
        (731,   186,    6,  0,  NULL),
        (732,   186,    7,  0,  NULL),
        (741,   189,    4,  0,  NULL),
        (742,   189,    5,  0,  NULL),
        (743,   189,    6,  0,  NULL),
        (744,   189,    7,  0,  NULL),
        (745,   190,    4,  0,  NULL),
        (746,   190,    5,  0,  NULL),
        (747,   190,    6,  0,  NULL),
        (748,   190,    7,  0,  NULL),
        (749,   191,    4,  0,  NULL),
        (750,   191,    5,  0,  NULL),
        (751,   191,    6,  0,  NULL),
        (752,   191,    7,  0,  NULL),
        (753,   192,    4,  0,  NULL),
        (754,   192,    5,  0,  NULL),
        (755,   192,    6,  0,  NULL),
        (756,   192,    7,  0,  NULL),
        (757,   193,    4,  0,  NULL),
        (758,   193,    5,  0,  NULL),
        (759,   193,    6,  0,  NULL),
        (760,   193,    7,  0,  NULL),
        (761,   194,    4,  0,  NULL),
        (762,   194,    5,  0,  NULL),
        (763,   194,    6,  0,  NULL),
        (764,   194,    7,  0,  NULL),
        (765,   195,    4,  0,  NULL),
        (766,   195,    5,  0,  NULL),
        (767,   195,    6,  0,  NULL),
        (768,   195,    7,  0,  NULL),
        (769,   196,    4,  0,  NULL),
        (770,   196,    5,  0,  NULL),
        (771,   196,    6,  0,  NULL),
        (772,   196,    7,  0,  NULL),
        (773,   197,    4,  0,  NULL),
        (774,   197,    5,  0,  NULL),
        (775,   197,    6,  0,  NULL),
        (776,   197,    7,  0,  NULL),
        (777,   198,    4,  0,  NULL),
        (778,   198,    5,  0,  NULL),
        (779,   198,    6,  0,  NULL),
        (780,   198,    7,  0,  NULL);");
       
    }

    public function down(Schema $schema): void
    {
    }
}
