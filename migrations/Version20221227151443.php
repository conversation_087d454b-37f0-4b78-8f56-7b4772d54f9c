<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221227151443 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE feature (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(255) NOT NULL, label VARCHAR(255) NOT NULL, brand VARCHAR(255) NOT NULL, country VARCHAR(255) NOT NULL, language VARCHAR(255) NOT NULL, source VARCHAR(255) NOT NULL, enabled TINYINT(1) DEFAULT NULL, is_early_adopter TINYINT(1) DEFAULT NULL, early_adopters_list LONGTEXT DEFAULT NULL COMMENT \'(DC2Type:array)\', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE feature_parameter (id INT AUTO_INCREMENT NOT NULL, feature_id INT NOT NULL, name VARCHAR(255) NOT NULL, value VARCHAR(255) NOT NULL, language VARCHAR(255) DEFAULT NULL, INDEX IDX_9C88518260E4B879 (feature_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE feature_parameter ADD CONSTRAINT FK_9C88518260E4B879 FOREIGN KEY (feature_id) REFERENCES feature (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE feature_parameter DROP FOREIGN KEY FK_9C88518260E4B879');
        $this->addSql('DROP TABLE feature');
        $this->addSql('DROP TABLE feature_parameter');
    }
}
