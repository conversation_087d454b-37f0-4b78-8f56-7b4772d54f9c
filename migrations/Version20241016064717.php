<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241016064717 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update country_id for ADMINISTRATION CENTRAL and insert localization data.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE `site` SET `country_id` = (SELECT id FROM `country` WHERE `code` = "XX")  WHERE `site`.`label` = "ADMINISTRATION CENTRAL"');

        $this->addSql('
            INSERT INTO local_key_json_release (brand_id, language_id, country_id, channel, imported_date, updated_date, status, filename)
            SELECT
                b.id AS brand_id,
                l.id AS language_id,
                (SELECT id FROM country WHERE code = "XX") AS country_id,
                channel.channel AS channel,
                NOW() AS imported_date,
                NOW() AS updated_date,
                "imported" AS status,
                "" AS filename
            FROM
                brand b
            CROSS JOIN
                language l
            JOIN
                (SELECT "WEB" AS channel UNION SELECT "APP" AS channel) AS channel
            WHERE
                l.is_reference = 1
                AND NOT EXISTS (
                    SELECT 1
                    FROM local_key_json_release lk
                    WHERE
                        lk.brand_id = b.id
                        AND lk.language_id = l.id
                        AND lk.country_id = (SELECT id FROM country WHERE code = "XX")
                        AND lk.channel = channel.channel
                )
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM local_key_json_release
            WHERE
                country_id = (SELECT id FROM country WHERE code = "XX")
                AND (channel = "WEB" OR channel = "APP")
                AND EXISTS (
                    SELECT 1
                    FROM brand b
                    WHERE b.id = local_key_json_release.brand_id
                )
                AND EXISTS (
                    SELECT 1
                    FROM language l
                    WHERE l.id = local_key_json_release.language_id
                    AND l.is_reference = 1
                )
        ');
        $this->addSql('UPDATE `site` SET `country_id` = null WHERE `site`.`label` = "ADMINISTRATION CENTRAL"');
    }
}
