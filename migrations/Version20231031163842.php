<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231031163842 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE widget_data (id INT AUTO_INCREMENT NOT NULL, widget_id INT NOT NULL, brand_id INT NOT NULL, country_id INT DEFAULT NULL, enabled TINYINT(1) NOT NULL, source VARCHAR(255) NOT NULL, INDEX IDX_3504F8DCFBE885E2 (widget_id), INDEX IDX_3504F8DC44F5D008 (brand_id), INDEX IDX_3504F8DCF92F3E70 (country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE widget_feature (id INT AUTO_INCREMENT NOT NULL, widget_id INT NOT NULL, brand_id INT NOT NULL, country_id INT DEFAULT NULL, code VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, enabled TINYINT(1) NOT NULL, label VARCHAR(255) NOT NULL, source VARCHAR(255) NOT NULL, INDEX IDX_11F872A7FBE885E2 (widget_id), INDEX IDX_11F872A744F5D008 (brand_id), INDEX IDX_11F872A7F92F3E70 (country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE widget_feature_attribute (id INT AUTO_INCREMENT NOT NULL, widget_feature_id INT NOT NULL, name VARCHAR(255) NOT NULL, value VARCHAR(255) NOT NULL, INDEX IDX_CF102C981419EF9E (widget_feature_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE widget_data ADD CONSTRAINT FK_3504F8DCFBE885E2 FOREIGN KEY (widget_id) REFERENCES widget (id)');
        $this->addSql('ALTER TABLE widget_data ADD CONSTRAINT FK_3504F8DC44F5D008 FOREIGN KEY (brand_id) REFERENCES brand (id)');
        $this->addSql('ALTER TABLE widget_data ADD CONSTRAINT FK_3504F8DCF92F3E70 FOREIGN KEY (country_id) REFERENCES country (id)');
        $this->addSql('ALTER TABLE widget_feature ADD CONSTRAINT FK_11F872A7FBE885E2 FOREIGN KEY (widget_id) REFERENCES widget (id)');
        $this->addSql('ALTER TABLE widget_feature ADD CONSTRAINT FK_11F872A744F5D008 FOREIGN KEY (brand_id) REFERENCES brand (id)');
        $this->addSql('ALTER TABLE widget_feature ADD CONSTRAINT FK_11F872A7F92F3E70 FOREIGN KEY (country_id) REFERENCES country (id)');
        $this->addSql('ALTER TABLE widget_feature_attribute ADD CONSTRAINT FK_CF102C981419EF9E FOREIGN KEY (widget_feature_id) REFERENCES widget_feature (id)');
        
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE widget_data DROP FOREIGN KEY FK_3504F8DCFBE885E2');
        $this->addSql('ALTER TABLE widget_data DROP FOREIGN KEY FK_3504F8DC44F5D008');
        $this->addSql('ALTER TABLE widget_data DROP FOREIGN KEY FK_3504F8DCF92F3E70');
        $this->addSql('ALTER TABLE widget_feature DROP FOREIGN KEY FK_11F872A7FBE885E2');
        $this->addSql('ALTER TABLE widget_feature DROP FOREIGN KEY FK_11F872A744F5D008');
        $this->addSql('ALTER TABLE widget_feature DROP FOREIGN KEY FK_11F872A7F92F3E70');
        $this->addSql('ALTER TABLE widget_feature_attribute DROP FOREIGN KEY FK_CF102C981419EF9E');
        $this->addSql('DROP TABLE widget_data');
        $this->addSql('DROP TABLE widget_feature');
        $this->addSql('DROP TABLE widget_feature_attribute');
    }
}
