<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230901084750 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        
        $this->addSql('ALTER TABLE profile ADD brand_id INT NULL');
        $this->addSql('ALTER TABLE profile ADD CONSTRAINT FK_8157AA0F44F5D008 FOREIGN KEY (brand_id) REFERENCES brand (id)');
        $this->addSql('CREATE INDEX IDX_8157AA0F44F5D008 ON profile (brand_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        
        $this->addSql('ALTER TABLE profile DROP FOREIGN KEY FK_8157AA0F44F5D008');
        $this->addSql('DROP INDEX IDX_8157AA0F44F5D008 ON profile');
        $this->addSql('ALTER TABLE profile DROP brand_id');
    }
}
