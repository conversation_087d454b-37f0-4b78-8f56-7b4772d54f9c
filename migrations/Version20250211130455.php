<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250211130455 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE menu DROP INDEX UNIQ_7D053A9360E4B879, ADD INDEX IDX_7D053A9360E4B879 (feature_id)');
        $this->addSql('ALTER TABLE role_menu DROP FOREIGN KEY FK_C3BA480714041B84');
        $this->addSql('ALTER TABLE role_menu DROP FOREIGN KEY FK_C3BA4807D60322AC');
        $this->addSql('ALTER TABLE role_menu ADD CONSTRAINT FK_C3BA4807CCD7E912 FOREIGN KEY (menu_id) REFERENCES menu (id)');
        $this->addSql('ALTER TABLE role_menu ADD CONSTRAINT FK_C3BA4807D60322AC FOREIGN KEY (role_id) REFERENCES role (id)');
        $this->addSql('ALTER TABLE role_menu RENAME INDEX idx_c3ba480714041b84 TO IDX_C3BA4807CCD7E912');
    }

    public function down(Schema $schema): void
    {
    }
}
