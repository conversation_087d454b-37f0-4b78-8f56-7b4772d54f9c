<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230202141630 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'languages data';
    }

    public function up(Schema $schema): void
    {
        // Insert Data into language Table
        $this->addSql("
        INSERT INTO `language` (`id`, `code`, `label`, `translate`) VALUES
            (1,	'fr',	'Français',	'Français'),
            (2,	'en',	'Anglais',	'English'),
            (3,	'it',	'Italien',	'Italiano'),
            (4,	'es',	'Espagnol',	'Español ,castellano'),
            (5,	'ae',	'Arabe',	'العربية'),
            (6,	'bg',	'Bulgare',	'български език'),
            (7,	'cs',	'Tch<PERSON><PERSON>',	'<PERSON><PERSON><PERSON> ,<PERSON><PERSON><PERSON><PERSON>'),
            (8,	'da',	'<PERSON><PERSON>',	'<PERSON><PERSON>'),
            (9,	'de',	'<PERSON>eman<PERSON>',	'<PERSON><PERSON><PERSON>'),
            (10,	'el',	'Grec',	'Ελληνικά'),
            (11,	'ee',	'Estonien',	'Eesti keel'),
            (12,	'ie',	'Irlandais',	'Gaeilge'),
            (13,	'il',	'Hébreu',	'עברית'),
            (14,	'in',	'Hindî',	'हिन्दी ,िंदी'),
            (15,	'hr',	'Croate',	'Hrvatski'),
            (16,	'hu',	'Hongrois',	'Magyar'),
            (17,	'id',	'Indonésien',	'Bahasa Indonesia'),
            (18,	'is',	'Islandais',	'Íslenska'),
            (19,	'jp',	'Japonais',	'日本語 (にほんご]'),
            (20,	'ge',	'Géorgien',	'ქართული'),
            (21,	'kp',	'Coréen',	'한국어 (韓國語] ,조선말 (朝鮮語]'),
            (22,	'la',	'Lao',	'ພາສາລາວ'),
            (23,	'lt',	'Lituanien',	'Lietuvių kalba'),
            (24,	'lv',	'Letton',	'Latviešu valoda'),
            (25,	'mg',	'Malgache',	'Fiteny malagasy'),
            (26,	'nl',	'Néerlandais',	'Nederlands'),
            (27,	'nb',	'Norvégien',	'Norsk'),
            (28,	'pl',	'Polonais',	'Polski'),
            (29,	'pt',	'Portugais',	'Português'),
            (30,	'ro',	'Roumain',	'Română'),
            (31,	'ru',	'Russe',	'русский язык'),
            (32,	'sk',	'Slovaque',	'Slovenčina'),
            (33,	'sl',	'Slovène',	'Slovenščina'),
            (34,	'al',	'Albanais',	'Shqip'),
            (35,	'sv',	'Suédois',	'Svenska'),
            (36,	'th',	'Thaï',	'ไทย'),
            (37,	'tr',	'Turc',	'Türkçe'),
            (38,	'vn',	'Vietnamien',	'Tiếng Việt'),
            (39,	'be',	'Wallon',	'Walon'),
            (40,	'cn',	'Chinois',	'中文, 汉语, 漢語'),
            (41,	'uk',	'Ukrainien',	'українська мова')
        ");
    }
}
