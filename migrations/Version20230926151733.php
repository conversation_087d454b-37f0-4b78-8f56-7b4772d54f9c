<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230926151733 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        //this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE menu (id INT AUTO_INCREMENT NOT NULL, parent_id INT DEFAULT NULL, label VARCHAR(255) DEFAULT NULL, icon_class VARCHAR(255) DEFAULT NULL, route_name VARCHAR(255) DEFAULT NULL, parameters VARCHAR(255) DEFAULT NULL, INDEX IDX_7D053A93B3750AF4 (parent_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE menu_profile (menu_id INT NOT NULL, profile_id INT NOT NULL, INDEX IDX_98CB7B1CCD7E912 (menu_id), INDEX IDX_98CB7B1CCFA12B8 (profile_id), PRIMARY KEY(menu_id, profile_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE menu ADD CONSTRAINT FK_7D053A93B3750AF4 FOREIGN KEY (parent_id) REFERENCES menu (id)');
        $this->addSql('ALTER TABLE menu_profile ADD CONSTRAINT FK_98CB7B1CCD7E912 FOREIGN KEY (menu_id) REFERENCES menu (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE menu_profile ADD CONSTRAINT FK_98CB7B1CCFA12B8 FOREIGN KEY (profile_id) REFERENCES profile (id) ON DELETE CASCADE');
        $this->addSql("
            INSERT INTO `menu` (`parent_id`, `label`, `icon_class`, `route_name`, `parameters`) VALUES
            (NULL, 'menu_dashboard', 'fas fa-tachometer-alt', 'admin', NULL),
            (NULL, 'menu_admin', 'fas fa-copy', '#', NULL),
            (2, 'menu_sites_list', 'far fa-circle', 'site_list', NULL),
            (2, 'menu_profiles', 'far fa-circle', 'profile_list', NULL),
            (2, 'menu_translation_key', 'fas fa-globe-europe', 'translation_key_list', NULL),
            (2, 'locale_translation_management', 'fas fa-circle', 'locale_translation_list', NULL);
        ");
        $this->addSql("
            INSERT INTO `menu_profile` (`menu_id`, `profile_id`)
            VALUES (1,1),
             (2,1),
             (3,1),
             (2,2),
             (4,2),
             (1,3),
             (2,3),
             (5,3);
        ");
        $this->addSql('CREATE TABLE profile_menu (id INT AUTO_INCREMENT NOT NULL, profile_id INT NOT NULL, menu_id INT NOT NULL, permission VARCHAR(255) DEFAULT NULL, INDEX IDX_CBCDB507CCFA12B8 (profile_id), INDEX IDX_CBCDB507CCD7E912 (menu_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE profile_menu ADD CONSTRAINT FK_CBCDB507CCFA12B8 FOREIGN KEY (profile_id) REFERENCES profile (id)');
        $this->addSql('ALTER TABLE profile_menu ADD CONSTRAINT FK_CBCDB507CCD7E912 FOREIGN KEY (menu_id) REFERENCES menu (id)');
        $this->addSql('ALTER TABLE menu_profile DROP FOREIGN KEY FK_98CB7B1CCFA12B8');
        $this->addSql('ALTER TABLE menu_profile DROP FOREIGN KEY FK_98CB7B1CCD7E912');
        $this->addSql('DROP TABLE menu_profile');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE menu DROP FOREIGN KEY FK_7D053A93B3750AF4');
        $this->addSql('ALTER TABLE menu_profile DROP FOREIGN KEY FK_98CB7B1CCD7E912');
        $this->addSql('ALTER TABLE menu_profile DROP FOREIGN KEY FK_98CB7B1CCFA12B8');
        $this->addSql('DROP TABLE menu');
        $this->addSql('DROP TABLE menu_profile');
        $this->addSql('ALTER TABLE profile CHANGE id profile_id INT AUTO_INCREMENT NOT NULL');
    }
}
