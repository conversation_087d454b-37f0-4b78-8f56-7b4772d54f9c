<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240403103120 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('
            SELECT id INTO @PARENT_APPLICATION_ID FROM `menu` WHERE label = "role_management";
            SELECT id INTO @PARENT_ROLE_ID FROM `menu` WHERE label = "application_services";
            
            UPDATE `menu` SET `label` = "access_management" WHERE `menu`.`id` = @PARENT_APPLICATION_ID;
            UPDATE `menu` SET `label` = "role_management" WHERE `menu`.`id` = @PARENT_ROLE_ID;
        ');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
