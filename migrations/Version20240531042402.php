<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240531042402 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE feature_setting ADD brand VARCHAR(50) DEFAULT NULL, ADD country VARCHAR(255) DEFAULT NULL');
        $this->addSql('UPDATE feature_setting SET brand = "XX" WHERE brand IS NULL');
    }

    public function down(Schema $schema): void
    {
    }
}
