<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241023125453 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Fix ADMINISTRATION CENTRAL site brand and country';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('UPDATE site SET brand_id = NULL, country_id = NULL WHERE label = \'ADMINISTRATION CENTRAL\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
