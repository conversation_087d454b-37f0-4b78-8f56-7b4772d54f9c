<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230927151615 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE menu ADD feature_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE menu ADD CONSTRAINT FK_7D053A9360E4B879 FOREIGN KEY (feature_id) REFERENCES feature_setting (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_7D053A9360E4B879 ON menu (feature_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE menu DROP FOREIGN KEY FK_7D053A9360E4B879');
        $this->addSql('DROP INDEX UNIQ_7D053A9360E4B879 ON menu');
        $this->addSql('ALTER TABLE menu DROP feature_id');
    }
}
