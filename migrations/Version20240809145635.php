<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240809145635 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE locale_translation CHANGE translation translation VARCHAR(2048) DEFAULT NULL');
        $this->addSql('ALTER TABLE reference_translation CHANGE translation translation VARCHAR(2048) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE locale_translation CHANGE translation translation VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE reference_translation CHANGE translation translation VARCHAR(255) DEFAULT NULL');
    }
}
