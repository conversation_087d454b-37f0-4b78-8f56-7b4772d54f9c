<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230612101839 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE media (id INT AUTO_INCREMENT NOT NULL, site_id INT DEFAULT NULL, directory_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, source VARCHAR(255) DEFAULT NULL, extension VARCHAR(5) NOT NULL, text_alt VARCHAR(255) DEFAULT NULL, copyright VARCHAR(255) DEFAULT NULL, size INT DEFAULT NULL, path VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, comment LONGTEXT DEFAULT NULL, created_by <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT NULL, INDEX IDX_6A2CA10CF6BD1646 (site_id), INDEX IDX_6A2CA10C2C94069F (directory_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE media_directory (id INT AUTO_INCREMENT NOT NULL, site_id INT DEFAULT NULL, parent_directory_id INT DEFAULT NULL, label VARCHAR(100) NOT NULL, path VARCHAR(100) NOT NULL, read_only TINYINT(1) NOT NULL, brand VARCHAR(2) DEFAULT NULL, INDEX IDX_6318F754F6BD1646 (site_id), INDEX IDX_6318F7547CFA5BB1 (parent_directory_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE media_service (id INT AUTO_INCREMENT NOT NULL, media_id INT DEFAULT NULL, site_id INT DEFAULT NULL, type VARCHAR(50) NOT NULL, INDEX IDX_804A1C9BEA9FDD75 (media_id), INDEX IDX_804A1C9BF6BD1646 (site_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE media ADD CONSTRAINT FK_6A2CA10CF6BD1646 FOREIGN KEY (site_id) REFERENCES site (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE media ADD CONSTRAINT FK_6A2CA10C2C94069F FOREIGN KEY (directory_id) REFERENCES media_directory (id)');
        $this->addSql('ALTER TABLE media_directory ADD CONSTRAINT FK_6318F754F6BD1646 FOREIGN KEY (site_id) REFERENCES site (id)');
        $this->addSql('ALTER TABLE media_directory ADD CONSTRAINT FK_6318F7547CFA5BB1 FOREIGN KEY (parent_directory_id) REFERENCES media_directory (id)');
        $this->addSql('ALTER TABLE media_service ADD CONSTRAINT FK_804A1C9BEA9FDD75 FOREIGN KEY (media_id) REFERENCES media (id)');
        $this->addSql('ALTER TABLE media_service ADD CONSTRAINT FK_804A1C9BF6BD1646 FOREIGN KEY (site_id) REFERENCES site (id)');
        $this->addSql('ALTER TABLE media_service ADD CONSTRAINT FK_804A1C9BBF396750 FOREIGN KEY (id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE role CHANGE label label VARCHAR(255) DEFAULT \'\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE media DROP FOREIGN KEY FK_6A2CA10CF6BD1646');
        $this->addSql('ALTER TABLE media DROP FOREIGN KEY FK_6A2CA10C2C94069F');
        $this->addSql('ALTER TABLE media_directory DROP FOREIGN KEY FK_6318F754F6BD1646');
        $this->addSql('ALTER TABLE media_directory DROP FOREIGN KEY FK_6318F7547CFA5BB1');
        $this->addSql('ALTER TABLE media_service DROP FOREIGN KEY FK_804A1C9BEA9FDD75');
        $this->addSql('ALTER TABLE media_service DROP FOREIGN KEY FK_804A1C9BF6BD1646');
        $this->addSql('ALTER TABLE media_service DROP FOREIGN KEY FK_804A1C9BBF396750');
        $this->addSql('DROP TABLE media');
        $this->addSql('DROP TABLE media_directory');
        $this->addSql('DROP TABLE media_service');
        $this->addSql('ALTER TABLE role CHANGE label label VARCHAR(255) DEFAULT \'[-Sans nom-]\' NOT NULL');
    }
}
