<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231016160802 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'add widget management menu';
    }

    public function up(Schema $schema): void
    {
        // CREATE MENU WIDGET
        $this->addSql('
                    DELETE FROM `menu` WHERE `menu`.`label` = "widget_administration";
                    DELETE FROM `menu` WHERE `menu`.`route_name` = "app_widget";
                    SELECT id INTO @PARENT_ID FROM `menu` WHERE label = "menu_admin";
                    INSERT INTO `menu` (`parent_id`, `label`, `icon_class`, `route_name`) VALUES (@PARENT_ID,   "widget_administration", "", "app_widget");
                    INSERT INTO `menu` (`parent_id`, `label`, `icon_class`, `route_name`) VALUES (@PARENT_ID,   "widget_management", "", "app_widget_management");'
                );
    }
}
