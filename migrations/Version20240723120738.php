<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240723120738 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE region_tmp (id INT AUTO_INCREMENT NOT NULL, country_code VARCHAR(50) CHARACTER SET latin1 DEFAULT NULL COLLATE `latin1_swedish_ci`, country VARCHAR(255) CHARACTER SET latin1 DEFAULT NULL COLLATE `latin1_swedish_ci`, region VARCHAR(50) CHARACTER SET latin1 DEFAULT NULL COLLATE `latin1_swedish_ci`, PRIMARY KEY(id)) DEFAULT CHARACTER SET latin1 COLLATE `latin1_swedish_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE region (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(10) NOT NULL, description VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE site ADD region_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE site ADD CONSTRAINT FK_694309E498260155 FOREIGN KEY (region_id) REFERENCES region (id)');
        $this->addSql('CREATE INDEX IDX_694309E498260155 ON site (region_id)');
        $this->addSql("INSERT INTO `region_tmp` (`country_code`, `country`, `region`) VALUES
            ('ad',	'Andorra',	'EMEA'),
            ('ae',	'United Arab Emirates',	'EMEA'),
            ('af',	'Afghanistan',	'EMEA'),
            ('ag',	'Antigua and Barbuda',	'LATAM'),
            ('ai',	'Anguilla',	'LATAM'),
            ('al',	'Albania',	'EMEA'),
            ('am',	'Armenia',	'EMEA'),
            ('ao',	'Angola',	'EMEA'),
            ('aq',	'Antarctica',	'EMEA'),
            ('ar',	'Argentina',	'LATAM'),
            ('as',	'American Samoa',	'APAC'),
            ('at',	'Austria',	'EMEA'),
            ('au',	'Australia',	'APAC'),
            ('aw',	'Aruba',	'LATAM'),
            ('ax',	'?land Islands',	'EMEA'),
            ('az',	'Azerbaijan',	'EMEA'),
            ('ba',	'Bosnia and Herzegovina',	'EMEA'),
            ('bb',	'Barbados',	'LATAM'),
            ('bd',	'Bangladesh',	'APAC'),
            ('be',	'Belgium',	'EMEA'),
            ('bf',	'Burkina Faso',	'EMEA'),
            ('bg',	'Bulgaria',	'EMEA'),
            ('bh',	'Bahrain',	'EMEA'),
            ('bi',	'Burundi',	'EMEA'),
            ('bj',	'Benin',	'EMEA'),
            ('bl',	'Saint Barth?lemy',	'EMEA'),
            ('bm',	'Bermuda',	'LATAM'),
            ('bn',	'Brunei Darussalam',	'APAC'),
            ('bo',	'Bolivia, Plurinational State of',	'LATAM'),
            ('br',	'Brazil',	'LATAM'),
            ('bs',	'Bahamas',	'LATAM'),
            ('bt',	'Bhutan',	'APAC'),
            ('bv',	'Bouvet Island',	'EMEA'),
            ('bw',	'Botswana',	'EMEA'),
            ('by',	'Belarus',	'EMEA'),
            ('bz',	'Belize',	'LATAM'),
            ('ca',	'Canada',	'NA'),
            ('cc',	'Cocos (Keeling) Islands',	'APAC'),
            ('cd',	'Congo, the Democratic Republic of the',	'EMEA'),
            ('cf',	'Central African Republic',	'EMEA'),
            ('cg',	'Congo',	'EMEA'),
            ('ch',	'Switzerland',	'EMEA'),
            ('ci',	'C?te d\'Ivoire',	'EMEA'),
            ('ck',	'Cook Islands',	'EMEA'),
            ('cl',	'Chile',	'LATAM'),
            ('cm',	'Cameroon',	'EMEA'),
            ('cn',	'China',	'APAC'),
            ('co',	'Colombia',	'LATAM'),
            ('cr',	'Costa Rica',	'LATAM'),
            ('cu',	'Cuba',	'LATAM'),
            ('cv',	'Cabo Verde',	'EMEA'),
            ('cw',	'Cura?ao',	'EMEA'),
            ('cx',	'Christmas Island',	'APAC'),
            ('cy',	'Cyprus',	'EMEA'),
            ('cz',	'Czechia',	'EMEA'),
            ('de',	'Germany',	'EMEA'),
            ('dj',	'Djibouti',	'EMEA'),
            ('dk',	'Denmark',	'EMEA'),
            ('dm',	'Dominica',	'LATAM'),
            ('do',	'Dominican Republic',	'LATAM'),
            ('dz',	'Algeria',	'EMEA'),
            ('ec',	'Ecuador',	'LATAM'),
            ('ee',	'Estonia',	'EMEA'),
            ('eg',	'Egypt',	'EMEA'),
            ('eh',	'Western Sahara',	'EMEA'),
            ('er',	'Eritrea',	'EMEA'),
            ('es',	'Spain',	'EMEA'),
            ('et',	'Ethiopia',	'EMEA'),
            ('fi',	'Finland',	'EMEA'),
            ('fj',	'Fiji',	'APAC'),
            ('fk',	'Falkland Islands (Malvinas)',	'LATAM'),
            ('fm',	'Micronesia, Federated States of',	'APAC'),
            ('fo',	'Faroe Islands',	'EMEA'),
            ('fr',	'France',	'EMEA'),
            ('ga',	'Gabon',	'EMEA'),
            ('gb',	'United Kingdom of Great Britain and Northern Ireland',	'EMEA'),
            ('gd',	'Grenada',	'LATAM'),
            ('ge',	'Georgia',	'EMEA'),
            ('gf',	'French Guiana',	'EMEA'),
            ('gg',	'Guernsey',	'EMEA'),
            ('gh',	'Ghana',	'EMEA'),
            ('gi',	'Gibralta',	'EMEA'),
            ('gl',	'Greenland',	'EMEA'),
            ('gm',	'Gambia',	'EMEA'),
            ('gn',	'Guinea',	'EMEA'),
            ('gp',	'Guadeloupe',	'EMEA'),
            ('gq',	'Equatorial Guinea',	'EMEA'),
            ('gr',	'Greece',	'EMEA'),
            ('gs',	'South Georgia and the South Sandwich Islands',	'LATAM'),
            ('gt',	'Guatemala',	'LATAM'),
            ('gu',	'Guam',	'APAC'),
            ('gw',	'Guinea-Bissau',	'EMEA'),
            ('gy',	'Guyana',	'LATAM'),
            ('hk',	'Hong Kong',	'APAC'),
            ('hm',	'Heard Island and McDonald Islands',	'EMEA'),
            ('hn',	'Honduras',	'LATAM'),
            ('hr',	'Croatia',	'EMEA'),
            ('ht',	'Haiti',	'LATAM'),
            ('hu',	'Hungary',	'EMEA'),
            ('id',	'Indonesia',	'APAC'),
            ('ie',	'Ireland',	'EMEA'),
            ('il',	'Israel',	'EMEA'),
            ('im',	'Isle of Man',	'EMEA'),
            ('in',	'India',	'APAC'),
            ('io',	'British Indian Ocean Territory',	'APAC'),
            ('iq',	'Iraq',	'EMEA'),
            ('ir',	'Iran, Islamic Republic of',	'EMEA'),
            ('is',	'Iceland',	'EMEA'),
            ('it',	'Italy',	'EMEA'),
            ('je',	'Jersey',	'EMEA'),
            ('jm',	'Jamaica',	'LATAM'),
            ('jo',	'Jordan',	'EMEA'),
            ('jp',	'Japan',	'APAC'),
            ('ke',	'Kenya',	'EMEA'),
            ('kg',	'Kyrgyzstan',	'EMEA'),
            ('kh',	'Cambodia',	'APAC'),
            ('ki',	'Kiribati',	'APAC'),
            ('km',	'Comoros',	'EMEA'),
            ('kn',	'Saint Kitts and Nevis',	'LATAM'),
            ('kp',	'Korea, Democratic People\'s Republic of',	'APAC'),
            ('kr',	'Korea, Republic of',	'APAC'),
            ('kw',	'Kuwait',	'EMEA'),
            ('ky',	'Cayman Islands',	'LATAM'),
            ('kz',	'Kazakhstan',	'EMEA'),
            ('la',	'Lao People\'s Democratic Republic',	'APAC'),
            ('lb',	'Lebanon',	'EMEA'),
            ('lc',	'Saint Lucia',	'LATAM'),
            ('li',	'Liechtenstein',	'EMEA'),
            ('lk',	'Sri Lanka',	'APAC'),
            ('lr',	'Liberia',	'EMEA'),
            ('ls',	'Lesotho',	'EMEA'),
            ('lt',	'Lithuania',	'EMEA'),
            ('lu',	'Luxembourg',	'EMEA'),
            ('lv',	'Latvia',	'EMEA'),
            ('ly',	'Libya',	'EMEA'),
            ('ma',	'Morocco',	'EMEA'),
            ('mc',	'Monaco',	'EMEA'),
            ('md',	'Moldova, Republic of',	'EMEA'),
            ('me',	'Montenegro',	'EMEA'),
            ('mf',	'Collectivity of Saint Martin',	'EMEA'),
            ('mg',	'Madagascar',	'EMEA'),
            ('mh',	'Marshall Islands',	'APAC'),
            ('mk',	'Macedonia, the former Yugoslav Republic of',	'EMEA'),
            ('ml',	'Mali',	'EMEA'),
            ('mm',	'Myanmar',	'APAC'),
            ('mn',	'Mongolia',	'APAC'),
            ('mo',	'Macao',	'APAC'),
            ('mp',	'Northern Mariana Islands',	'APAC'),
            ('mq',	'Martinique',	'EMEA'),
            ('mr',	'Mauritania',	'EMEA'),
            ('ms',	'Montserrat',	'LATAM'),
            ('mt',	'Malta',	'EMEA'),
            ('mu',	'Mauritius',	'EMEA'),
            ('mv',	'Maldives',	'APAC'),
            ('mw',	'Malawi',	'EMEA'),
            ('mx',	'Mexico',	'LATAM'),
            ('my',	'Malaysia',	'APAC'),
            ('mz',	'Mozambique',	'EMEA'),
            ('na',	'Namibia',	'EMEA'),
            ('nc',	'New Caledonia',	'EMEA'),
            ('ne',	'Niger',	'EMEA'),
            ('nf',	'Norfolk Island',	'APAC'),
            ('ng',	'Nigeria',	'EMEA'),
            ('ni',	'Nicaragua',	'LATAM'),
            ('nl',	'Netherlands',	'EMEA'),
            ('no',	'Norway',	'EMEA'),
            ('np',	'Nepal',	'APAC'),
            ('nr',	'Nauru',	'APAC'),
            ('nu',	'Niue',	'APAC'),
            ('nz',	'New Zealand',	'APAC'),
            ('om',	'Oman',	'EMEA'),
            ('pa',	'Panama',	'LATAM'),
            ('pe',	'Peru',	'LATAM'),
            ('pf',	'French Polynesia',	'EMEA'),
            ('pg',	'Papua New Guinea',	'APAC'),
            ('ph',	'Philippines',	'APAC'),
            ('pk',	'Pakistan',	'APAC'),
            ('pl',	'Poland',	'EMEA'),
            ('pm',	'Saint Pierre and Miquelon',	'EMEA'),
            ('pn',	'Pitcairn',	'APAC'),
            ('pr',	'Puerto Rico',	'LATAM'),
            ('ps',	'Palestine, State of',	'EMEA'),
            ('pt',	'Portugal',	'EMEA'),
            ('pw',	'Palau',	'APAC'),
            ('py',	'Paraguay',	'LATAM'),
            ('qa',	'Qatar',	'EMEA'),
            ('re',	'R?union',	'EMEA'),
            ('ro',	'Romania',	'EMEA'),
            ('rs',	'Serbia',	'EMEA'),
            ('ru',	'Russian Federation',	'EMEA'),
            ('rw',	'Rwanda',	'EMEA'),
            ('sa',	'Saudi Arabia',	'EMEA'),
            ('sb',	'Solomon Islands',	'APAC'),
            ('sc',	'Seychelles',	'EMEA'),
            ('sd',	'Sudan',	'EMEA'),
            ('se',	'Sweden',	'EMEA'),
            ('sg',	'Singapore',	'APAC'),
            ('sh',	'Saint Helena, Ascension and Tristan da Cunha',	'EMEA'),
            ('si',	'Slovenia',	'EMEA'),
            ('sk',	'Slovakia',	'EMEA'),
            ('sl',	'Sierra Leone',	'EMEA'),
            ('sm',	'San Marino',	'EMEA'),
            ('sn',	'Senegal',	'EMEA'),
            ('so',	'Somalia',	'EMEA'),
            ('sr',	'Suriname',	'LATAM'),
            ('ss',	'South Sudan',	'EMEA'),
            ('st',	'Sao Tome and Principe',	'EMEA'),
            ('sv',	'El Salvador',	'LATAM'),
            ('sx',	'Sint Maarten',	'EMEA'),
            ('sy',	'Syrian Arab Republic',	'EMEA'),
            ('sz',	'Swaziland',	'EMEA'),
            ('tc',	'Turks and Caicos Islands',	'LATAM'),
            ('td',	'Chad',	'EMEA'),
            ('tf',	'French Southern Territories',	'EMEA'),
            ('tg',	'Togo',	'EMEA'),
            ('th',	'Thailand',	'APAC'),
            ('tj',	'Tajikistan',	'EMEA'),
            ('tk',	'Tokelau',	'APAC'),
            ('tl',	'Timor-Leste',	'APAC'),
            ('tm',	'Turkmenistan',	'EMEA'),
            ('tn',	'Tunisia',	'EMEA'),
            ('to',	'Tonga',	'APAC'),
            ('tr',	'Turkey',	'EMEA'),
            ('tt',	'Trinidad and Tobago',	'LATAM'),
            ('tv',	'Tuvalu',	'APAC'),
            ('tw',	'Taiwan, Province of China',	'APAC'),
            ('tz',	'Tanzania, United Republic of',	'EMEA'),
            ('ua',	'Ukraine',	'EMEA'),
            ('ug',	'Uganda',	'EMEA'),
            ('um',	'United States Minor Outlying Islands',	'APAC'),
            ('us',	'United States of America',	'NA'),
            ('uy',	'Uruguay',	'LATAM'),
            ('uz',	'Uzbekistan',	'EMEA'),
            ('va',	'Holy See',	'EMEA'),
            ('vc',	'Saint Vincent and the Grenadines',	'LATAM'),
            ('ve',	'Venezuela, Bolivarian Republic of',	'LATAM'),
            ('vg',	'Virgin Islands, British',	'LATAM'),
            ('vi',	'Virgin Islands, U.S.',	'LATAM'),
            ('vn',	'Vietnam',	'APAC'),
            ('vu',	'Vanuatu',	'APAC'),
            ('wf',	'Wallis and Futuna',	'EMEA'),
            ('ws',	'Samoa',	'APAC'),
            ('ye',	'Yemen',	'EMEA'),
            ('yt',	'Mayotte',	'EMEA'),
            ('za',	'South Africa',	'EMEA'),
            ('zm',	'Zambia',	'EMEA'),
            ('zw',	'Zimbabwe',	'EMEA')"
        );


        $this->addSql("INSERT INTO `region` (`code`, `description`) VALUES
            ('EMEA','Europe, Middle East, and Africa'),
            ('LATAM','Latin America'),
            ('APAC','Asia-Pacific'),
            ('NA','North America'),
            ('OTHER','Other')"
        );

        $this->addSql("UPDATE site s
            INNER JOIN country c ON s.country_id = c.id
            INNER JOIN region_tmp rt ON c.code = UPPER(rt.country_code)
            INNER JOIN region r ON r.code = rt.region
            SET s.region_id = r.id"
        );
        $this->addSql("
            SELECT id INTO @OTHER_ID FROM `region` WHERE code = 'OTHER';
            UPDATE site SET region_id = @OTHER_ID WHERE region_id IS NULL;"
        );
        $this->addSql('DROP TABLE region_tmp');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE site DROP FOREIGN KEY FK_694309E498260155');
        $this->addSql('DROP INDEX IDX_694309E498260155 ON site');
        $this->addSql('ALTER TABLE site DROP region_id');
        $this->addSql('DROP TABLE region');
    }
}
