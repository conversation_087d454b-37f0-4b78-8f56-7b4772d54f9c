<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230224140116 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE reference_translation (id INT AUTO_INCREMENT NOT NULL, translation_key_id INT NOT NULL, language_id INT NOT NULL, translation VARCHAR(255) DEFAULT NULL, INDEX IDX_525E075BD07ED992 (translation_key_id), INDEX IDX_525E075B82F1BAF4 (language_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE reference_translation ADD CONSTRAINT FK_525E075BD07ED992 FOREIGN KEY (translation_key_id) REFERENCES translation_key (id)');
        $this->addSql('ALTER TABLE reference_translation ADD CONSTRAINT FK_525E075B82F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)');
        $this->addSql('ALTER TABLE language DROP translate');
        $this->addSql('ALTER TABLE translation_key DROP FOREIGN KEY FK_AADCBD5679D01268');
        $this->addSql('DROP INDEX IDX_AADCBD5679D01268 ON translation_key');
        $this->addSql('ALTER TABLE translation_key DROP reference_translations_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
       $this->addSql('ALTER TABLE reference_translation DROP FOREIGN KEY FK_525E075BD07ED992');
        $this->addSql('ALTER TABLE reference_translation DROP FOREIGN KEY FK_525E075B82F1BAF4');
        $this->addSql('DROP TABLE reference_translation');
        $this->addSql('ALTER TABLE language ADD translate VARCHAR(50) DEFAULT NULL');
        $this->addSql('ALTER TABLE translation_key ADD reference_translations_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE translation_key ADD CONSTRAINT FK_AADCBD5679D01268 FOREIGN KEY (reference_translations_id) REFERENCES locale_translation (id)');
        $this->addSql('CREATE INDEX IDX_AADCBD5679D01268 ON translation_key (reference_translations_id)');
    }
}
