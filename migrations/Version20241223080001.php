<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241223080001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $rows = $this->connection->fetchAllAssociative('SELECT id, code FROM brand WHERE code <> :condition', [
            'condition' => 'XX',
        ]);

        $country = $this->connection->fetchAssociative('SELECT id FROM country WHERE code = :condition', [
            'condition' => 'XX',
        ]);

        $region = $this->connection->fetchAssociative('SELECT id FROM region WHERE code = :condition', [
            'condition' => 'OTHER',
        ]);

        foreach($rows as $row) {
            $this->addSql(
                "INSERT INTO site 
                    (brand_id, prefered_language_id, label, timezone, ldap_code, domtom, civilites_customer_at_active, reversed_name_order, distance_unit, consumption_unit, cost, volume_unit, date_format, hour_format, country_id, region_id) 
                VALUES(:brandId, 2, :label, 'UTC', NULL, 0, 1, 0, 'KM', 'L/100', 'L', 'L', 'JJ/MM/AAAA', '0..24', :country, :region)", [
                    'brandId' => $row['id'],
                    'label'   => 'ADMINISTRATION CENTRAL ' . $row['code'],
                    'country' => $country['id'],
                    'region'  => $region['id']
                ]
            );            
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        // Delete rows added to `site_language`
        $this->addSql('DELETE FROM site_language WHERE site_id IN (SELECT id FROM site WHERE label LIKE :prefix)', [
            'prefix' => 'ADMINISTRATION CENTRAL %',
        ]);

        // Delete rows added to `profile`
        $this->addSql('DELETE FROM profile WHERE site_id IN (SELECT id FROM site WHERE label LIKE :prefix)', [
            'prefix' => 'ADMINISTRATION CENTRAL %',
        ]);

        // Delete rows added to `site`
        $this->addSql('DELETE FROM site WHERE label LIKE :prefix', [
            'prefix' => 'ADMINISTRATION CENTRAL %',
        ]);
    }
}
