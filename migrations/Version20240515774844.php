<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240515774844 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
        SELECT id INTO @ADMIN_MENU_ID FROM `menu` WHERE label = 'menu_admin';

        INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`) 
        VALUES (NULL, @ADMIN_MENU_ID, 'local_translation_dashboard', 'fas fa-tachometer-alt', 'local_translation_dashboard', NULL, NULL);

        SELECT id INTO @LOCAL_TRANSLATION_DASHBOARD_MENU_ID FROM `menu` WHERE label = 'local_translation_dashboard';
        SELECT id INTO @SUPER_ADMIN_ID FROM `role` WHERE label = 'Super Administrator';
        SELECT id INTO @TECHNICAL_ADMIN_ID FROM `role` WHERE label = 'Technical Administrator';
        SELECT id INTO @FUNCTIONAL_ADMIN_ID FROM `role` WHERE label = 'Functional Administrator';
        SELECT id INTO @READER_ID FROM `role` WHERE label = 'Reader';

        INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`) 
        VALUES (NULL, @SUPER_ADMIN_ID, @LOCAL_TRANSLATION_DASHBOARD_MENU_ID, 'R');

        INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`) 
        VALUES (NULL, @TECHNICAL_ADMIN_ID, @LOCAL_TRANSLATION_DASHBOARD_MENU_ID, 'R');

        INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`) 
        VALUES (NULL, @FUNCTIONAL_ADMIN_ID, @LOCAL_TRANSLATION_DASHBOARD_MENU_ID, 'R');

        INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`) 
        VALUES (NULL, @READER_ID, @LOCAL_TRANSLATION_DASHBOARD_MENU_ID, 'R');
        ");
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }    

}
