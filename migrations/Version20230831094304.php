<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230831094304 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE idp_role (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE idp_role_role (idp_role_id INT NOT NULL, role_id INT NOT NULL, INDEX IDX_14A4A225AFD86236 (idp_role_id), INDEX IDX_14A4A225D60322AC (role_id), PRIMARY KEY(idp_role_id, role_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE idp_role_role ADD CONSTRAINT FK_14A4A225D60322AC FOREIGN KEY (role_id) REFERENCES role (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE idp_role_role ADD CONSTRAINT FK_14A4A225AFD86236 FOREIGN KEY (idp_role_id) REFERENCES idp_role (id) ON DELETE CASCADE');
        $this->addSql("
            INSERT INTO `idp_role` (`id`, `name`) VALUES
            (1, 'SUPERADMIN'),
            (2, 'ADMINTECH'),
            (3, 'ADMINFUNC'),
            (4, 'OPERATIONS'),
            (5, 'WEBMASTER'),
            (6, 'VIEWER'),
            (7, 'NP-ADMIN'),
            (8, 'DEV');");
        $this->addSql("
            INSERT INTO `idp_role_role` (`idp_role_id`, `role_id`) VALUES
            (1, 1),
            (1, 2),
            (1, 3),
            (1, 5),
            (1, 6),
            (2, 2),
            (2, 7),
            (3, 3),
            (3, 6),
            (4, 5),
            (5, 6),
            (6, 4),
            (7, 1),
            (7, 2),
            (7, 3),
            (7, 5),
            (7, 6),
            (8, 1),
            (8, 2),
            (8, 3),
            (8, 4),
            (8, 5),
            (8, 6),
            (8, 7);");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE app_service DROP FOREIGN KEY FK_CC01A9FD60322AC');
        $this->addSql('ALTER TABLE culture CHANGE code code VARCHAR(5) DEFAULT NULL');
        $this->addSql('ALTER TABLE feature CHANGE brand brand VARCHAR(255) NOT NULL, CHANGE source source VARCHAR(255) NOT NULL, CHANGE enabled enabled TINYINT(1) DEFAULT NULL, CHANGE is_early_adopter is_early_adopter TINYINT(1) DEFAULT NULL');
        $this->addSql('ALTER TABLE idp_role_role DROP FOREIGN KEY FK_14A4A225D60322AC');
        $this->addSql('ALTER TABLE profile DROP FOREIGN KEY FK_8157AA0FD60322AC');
        $this->addSql('ALTER TABLE role MODIFY role_id INT NOT NULL');
        $this->addSql('DROP INDEX `PRIMARY` ON role');
        $this->addSql('ALTER TABLE role ADD PRIMARY KEY (id)');
    }
}
