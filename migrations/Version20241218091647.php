<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241218091647 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE role_menu DROP FOREIGN KEY FK_C3BA4807D60322AC');
        $this->addSql('ALTER TABLE role_menu DROP FOREIGN KEY FK_C3BA480714041B84');
        $this->addSql('ALTER TABLE role_menu ADD CONSTRAINT FK_C3BA4807D60322AC FOREIGN KEY (role_id) REFERENCES role (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE role_menu ADD CONSTRAINT FK_C3BA480714041B84 FOREIGN KEY (menu_id) REFERENCES menu (id) ON DELETE CASCADE');
        $this->addSql('DELETE m1 FROM menu m1 JOIN menu m2 WHERE m1.feature_id = m2.feature_id AND m1.id > m2.id');
        $this->addSql('ALTER TABLE menu DROP INDEX IDX_7D053A9360E4B879, ADD UNIQUE INDEX UNIQ_7D053A9360E4B879 (feature_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE menu DROP INDEX UNIQ_7D053A9360E4B879, ADD INDEX IDX_7D053A9360E4B879 (feature_id)');
    }
}
