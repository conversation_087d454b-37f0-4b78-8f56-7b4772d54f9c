<?php

require_once 'vendor/autoload.php';

use MongoDB\Client;

echo "=== MongoDB Docker Connection Test ===\n\n";

try {
    // Test MongoDB connection using Docker container name
    $mongoUrl = "****************************************************************************";
    echo "🔗 Connecting to MongoDB with URL: $mongoUrl\n";

    $client = new Client($mongoUrl);
    $database = $client->selectDatabase('spaceDb');
    
    // Test connection with ping
    $result = $database->command(['ping' => 1]);
    echo "✅ MongoDB connection successful!\n";
    echo "📊 Ping result: " . json_encode($result->toArray()) . "\n\n";
    
    // List existing collections
    echo "📋 Listing existing collections:\n";
    $collections = iterator_to_array($database->listCollections());
    if (empty($collections)) {
        echo "   No collections found (this is normal for a new database)\n";
    } else {
        foreach ($collections as $collection) {
            echo "   - " . $collection->getName() . "\n";
        }
    }
    echo "\n";
    
    // Test basic operations
    $collection = $database->selectCollection('docker_test_collection');
    
    // Insert a test document
    echo "📝 Testing document insertion...\n";
    $testDoc = [
        'test_field' => 'docker_test_value',
        'timestamp' => new MongoDB\BSON\UTCDateTime(),
        'environment' => 'docker',
        'container' => 'app_space_back',
        'data' => [
            'framework' => 'Symfony',
            'database' => 'MongoDB',
            'setup' => 'Docker Compose'
        ]
    ];
    
    $insertResult = $collection->insertOne($testDoc);
    echo "✅ Document inserted with ID: " . $insertResult->getInsertedId() . "\n";
    
    // Find the document
    echo "🔍 Testing document retrieval...\n";
    $document = $collection->findOne(['_id' => $insertResult->getInsertedId()]);
    echo "✅ Document found: " . json_encode($document, JSON_PRETTY_PRINT) . "\n\n";
    
    // Count documents
    $count = $collection->countDocuments();
    echo "📊 Total documents in test collection: $count\n";
    
    // Test aggregation
    echo "🔄 Testing aggregation pipeline...\n";
    $pipeline = [
        ['$match' => ['environment' => 'docker']],
        ['$group' => [
            '_id' => '$environment',
            'count' => ['$sum' => 1],
            'latest' => ['$max' => '$timestamp']
        ]]
    ];
    
    $aggregationResult = $collection->aggregate($pipeline);
    $aggregationDocs = $aggregationResult->toArray();
    echo "✅ Aggregation result: " . json_encode($aggregationDocs, JSON_PRETTY_PRINT) . "\n\n";
    
    // Clean up - delete the test document
    echo "🧹 Cleaning up test data...\n";
    $deleteResult = $collection->deleteOne(['_id' => $insertResult->getInsertedId()]);
    echo "✅ Test document deleted. Deleted count: " . $deleteResult->getDeletedCount() . "\n\n";
    
    // Test connection to existing collections (if any)
    echo "🔍 Testing access to existing collections...\n";
    $vehicleLabelCollection = $database->selectCollection('vehicleLabel');
    $vehicleCount = $vehicleLabelCollection->countDocuments();
    echo "📊 Documents in 'vehicleLabel' collection: $vehicleCount\n";
    
    if ($vehicleCount > 0) {
        echo "📄 Sample document from vehicleLabel:\n";
        $sampleDoc = $vehicleLabelCollection->findOne();
        echo json_encode($sampleDoc, JSON_PRETTY_PRINT) . "\n";
    }
    
    echo "\n🎉 All MongoDB Docker tests passed successfully!\n";
    echo "🌐 You can access Mongo Express at: http://back-dev.space.com:8083\n";
    echo "   Username: admin, Password: admin\n\n";
    
    echo "📝 Next steps:\n";
    echo "   1. Your MongoDB service is running in Docker\n";
    echo "   2. Access your Symfony app at: http://back-dev.space.com:5555/debug_api/\n";
    echo "   3. Test the form to see MongoDB integration in action\n";
    echo "   4. Use Mongo Express to browse your database\n";
    
} catch (Exception $e) {
    echo "❌ MongoDB connection failed: " . $e->getMessage() . "\n";
    echo "📋 Troubleshooting steps:\n";
    echo "   1. Make sure MongoDB container is running: docker ps | grep mongodb_space\n";
    echo "   2. Check container logs: docker logs mongodb_space\n";
    echo "   3. Verify network connectivity: docker exec -it app_space_back ping mongodb_space\n";
    echo "   4. Check if MongoDB port is accessible: docker exec -it app_space_back nc -zv mongodb_space 27017\n";
    echo "\nStack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
