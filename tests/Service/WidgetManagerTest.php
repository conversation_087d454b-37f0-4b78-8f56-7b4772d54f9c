<?php

declare(strict_types=1);

namespace App\Tests\Service;

use App\Entity\AccessLog;
use App\Entity\Brand;
use App\Entity\Channel;
use App\Entity\Widget;
use App\Entity\Country;
use App\Entity\Language;
use App\Entity\WidgetData;
use Doctrine\ORM\UnitOfWork;
use Psr\Log\LoggerInterface;
use App\Entity\WidgetFeature;
use App\Service\AWSS3Service;
use App\Service\WidgetManager;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Uid\Uuid;
use App\Repository\BrandRepository;
use App\Repository\WidgetRepository;
use App\Entity\WidgetFeatureAttribute;
use App\Repository\LanguageRepository;
use App\Repository\WidgetDataRepository;
use App\Service\LabelTranslationManager;
use Doctrine\ORM\EntityManagerInterface;
use App\Repository\WidgetFeatureRepository;
use Doctrine\Common\Collections\Collection;
use App\Tests\Builder\Entity\LanguageBuilder;
use Doctrine\Common\Collections\ArrayCollection;
use App\Repository\WidgetFeatureAttributeRepository;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Factory\TranslationKeyFactory;
use App\Repository\AccessLogRepository;
use App\Repository\ChannelRepository;
use DateTime;
use DateTimeInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class WidgetManagerTest extends TestCase
{
    private EntityManagerInterface $entityManager;
    private WidgetManager $widgetManager;
    private LoggerInterface $logger;
    private AWSS3Service $s3Service;
    private WidgetRepository $widgetRepo;
    private WidgetDataRepository $widgetDataRepo;
    private BrandRepository $brandRepo;
    private WidgetFeatureRepository $widgetFeatureRepo;
    private WidgetFeatureAttributeRepository $widgetFeatureAttributeRepo;
    private TranslationKeyFactory $translationKeyFactory;
    private ChannelRepository $channelRepo;
    private ValidatorInterface $validator;
    private SerializerInterface $serializer;
    private NormalizerInterface $normalizer;
    private AccessLogRepository $accessLogRepository;

    protected function setUp(): void
    {
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->s3Service = $this->createMock(AWSS3Service::class);
        $this->widgetRepo = $this->createMock(WidgetRepository::class);
        $this->widgetDataRepo = $this->createMock(WidgetDataRepository::class);
        $this->brandRepo = $this->createMock(BrandRepository::class);
        $this->widgetFeatureRepo = $this->createMock(WidgetFeatureRepository::class);
        $this->widgetFeatureAttributeRepo = $this->createMock(WidgetFeatureAttributeRepository::class);
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->accessLogRepository = $this->createMock(AccessLogRepository::class);
        $unityOfWork = $this->createMock(UnitOfWork::class);
        $unityOfWork->expects($this->any())->method("getOriginalEntityData")->willReturn(['name' => 'test']);
        $this->entityManager->expects($this->any())->method("getUnitOfWork")->willReturn($unityOfWork);
        $this->brandRepo->expects($this->any())->method("findAll")->willReturn([$this->getBrand()]);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->translationKeyFactory = $this->createMock(TranslationKeyFactory::class);
        $channels = [$this->createMock(Channel::class)];
        $this->channelRepo = $this->createMock(ChannelRepository::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->normalizer = new ObjectNormalizer();

        $this->channelRepo->expects($this->any())->method('findAll')->willReturn($channels);


        $this->widgetManager = new WidgetManager(
            $this->entityManager,
            $this->widgetRepo,
            $this->widgetFeatureRepo,
            $this->widgetFeatureAttributeRepo,
            $this->createMock(LanguageRepository::class),
            $this->widgetDataRepo,
            $this->s3Service,
            $this->createMock(LabelTranslationManager::class),
            'testBucket',
            $this->brandRepo,
            $this->validator,
            $this->translationKeyFactory,
            $this->channelRepo,
            $this->serializer,
            $this->normalizer,
            $this->accessLogRepository
        );
        $this->widgetManager->setLogger($this->logger);
    }

    public function testGetWidgetS3ResourceContent(): void
    {
        $relativeUrl = 'testUrl';
        $expectedContent = 'testContent';

        $this->s3Service->expects($this->once())
            ->method('getObjectContent')
            ->with('testBucket', $relativeUrl)
            ->willReturn($expectedContent);

        $result = $this->widgetManager->getWidgetS3ResourceContent($relativeUrl);

        $this->assertEquals($expectedContent, $result);
    }

    public function getWidget(): Widget
    {
        $widget = new Widget();
        $mockUuid = $this->createMock(Uuid::class);

        // Define the mock UUID's behavior (return a specific UUID string)
        $mockUuid->method('__toString')
            ->willReturn('123e4567-e89b-12d3-a456-************');
        $widget->setWguid($mockUuid);
        $widget->setDescription("Testing");
        $widget->setName("Test");
        $widget->setVersion("1");
        $widget->setType("test");
        $feature_configs = [
            'labels' => [
                'WA_assistance_title',
            ],
            'sources' => [
                'WEB',
            ],
            'features' => [
                [
                    'code' => 'roadside',
                    'title' => 'Roadside assistance',
                    'fields' => [
                        [
                            'name' => 'phone',
                            'type' => 'TextType',
                            'options' => [
                                'label' => 'Phone',
                                'required' => false,
                                'attr' => [
                                    'multiValue' => true
                                ]
                            ],
                            'section' => 'default',
                            'translatable' => false,
                        ],
                        [
                            'name' => 'Cards',
                            'type' => 'MultiFieldType',
                            'options' => [
                                'label' => 'Carousel',
                                'model' => [
                                    'title' => [
                                        'type' => 'TextType',
                                        'required' => true,
                                        'translatable' => true
                                    ],
                                    'order' => [
                                        'type' => 'IntegerType',
                                        'required' => true
                                    ],
                                    'imageUrl' => 'ImageType',
                                    'redirectUrl' => [
                                        'type' => 'TextType',
                                        'required' => true
                                    ]
                                ]
                            ],
                            'section' => 'default',
                            'translatable' => false,
                        ],
                    ],
                    'enabled' => true,
                ],
            ],
            'widgetname' => 'brand_assistance',
        ];

        $widget->setFeaturesConfiguration($feature_configs);
        // $widget->addWidgetData($this->getWidgetData());
        // $widget->addWidgetFeature($this->getWidgetFeature());
        return $widget;
    }

    public function getBrand(): Brand
    {
        $brand = new Brand();
        $brand->setCode("AC");
        $brand->setName("Citroen");
        return $brand;
    }

    public function getCountry(): Country
    {
        $country = new Country();
        $country->setCode("FR");
        $country->setName("France");
        return $country;
    }

    public function getLanguage(): Language
    {
        $referenceLanguage = new Language();
        $referenceLanguage->setCode("fr");
        $referenceLanguage->setIsReference(true);
        $referenceLanguage->setLabel("french");
        return $referenceLanguage;
    }

    public function getWidgetData(): WidgetData
    {
        $widgetData = new WidgetData();
        $widget = $this->getWidget();
        $languages = $this->createMock(Collection::class);
        $languages->expects($this->any())->method("isEmpty")->willReturn(true);
        $brand = $this->getBrand();
        $country = $this->getCountry();
        $widgetData->setBrand($brand);
        $widgetData->setWidget($widget);
        $widgetData->setSource("WEB");
        $widgetData->setCountry($country);
        $widgetData->setEnabled(false);
        return $widgetData;
    }

    public function getWidgetFeature(): WidgetFeature
    {
        $feature = new WidgetFeature();
        $attribute2 = new WidgetFeatureAttribute();
        $attribute2->setName("name2");
        $attribute2->setValue("test2");
        $attribute1 = new WidgetFeatureAttribute();
        $attribute1->setName("name2");
        $attribute1->setValue("test2");
        $attribute1->setLanguage($this->getLanguage());
        $attribute3 = new WidgetFeatureAttribute();
        $attribute3->setName("name2");
        $attribute3->setValue('[{"type":"isMultifield","id":null,"url":null,"image_input":null,"image":null,"label":null,"parent":null}]');
        $attribute3->setType(WidgetManager::MULTI_FIELDS_TYPE);
        $attribute4 = new WidgetFeatureAttribute();
        $attribute4->setName("name2");
        $attribute4->setValue('[{"type":"isMultifield","id":"test","url":null,"image_input":null,"image":null,"label":null,"parent":null}]');
        $attribute4->setType(WidgetManager::MULTI_FIELDS_TYPE);
        $attribute5 = new WidgetFeatureAttribute();
        $attribute5->setName("name2");
        $attribute5->setValue('[{"type":"isMultifield","image_input":null}]');
        $attribute5->setType(WidgetManager::MULTI_FIELDS_TYPE);
        $feature->setWidget($this->getWidget());
        $feature->setBrand($this->getBrand());
        $feature->setCountry($this->getCountry());
        $feature->setSource("WEB");
        $feature->setLabel("testing");
        $feature->setEnabled(false);
        $feature->addAttribute($attribute2);
        $feature->addAttribute($attribute1);
        $feature->addAttribute($attribute3);
        $feature->addAttribute($attribute4);
        $feature->addAttribute($attribute5);
        return $feature;
    }
    public function testGetWidgetS3ResourceContentReturnsEmptyStringOnFailure(): void
    {
        $relativeUrl = 'testUrl';

        $this->s3Service->expects($this->once())
            ->method('getObjectContent')
            ->with('testBucket', $relativeUrl)
            ->willReturn(false);

        $result = $this->widgetManager->getWidgetS3ResourceContent($relativeUrl);

        $this->assertEquals('', $result);
    }

    public function testGetWidgetS3ResourcesStatus(): void
    {
        $widget = $this->createMock(Widget::class);
        $widget->method('getName')->willReturn('testWidget');
        $brand = $this->createMock(Brand::class);
        $brand->method('getCode')->willReturn('testBrand');
        $country = $this->createMock(Country::class);
        $country->method('getCode')->willReturn('testCountry');

        $languageBuilder = new LanguageBuilder();
        $languages = new ArrayCollection([
            $languageBuilder->withCode('en')->build(),
            $languageBuilder->withCode('fr')->build(),
        ]);
        $referenceLanguage = $this->createMock(Language::class);

        $widget->method('getFeaturesConfiguration')->willReturn(['sources' => ['APP', 'WEB']]);

        $this->s3Service->expects($this->exactly(4))
            ->method('getObjectMetaData')
            ->withConsecutive(
                ['testBucket', 'app/testbrand/en-TESTCOUNTRY/testwidget/widget-settings.json'],
                ['testBucket', 'app/testbrand/fr-TESTCOUNTRY/testwidget/widget-settings.json'],
                ['testBucket', 'web/testbrand/en-TESTCOUNTRY/testwidget/widget-settings.json'],
                ['testBucket', 'web/testbrand/fr-TESTCOUNTRY/testwidget/widget-settings.json']
            )
            ->willReturnOnConsecutiveCalls(
                ['creationDate' => '2024-01-01 12:34:04', 'lastUpdateDate' => '2024-01-31 02:53:45'],
                ['creationDate' => '2024-01-01 12:34:04', 'lastUpdateDate' => '2024-01-31 02:53:45'],
                ['creationDate' => '2024-01-01 12:34:04', 'lastUpdateDate' => '2024-01-31 02:53:45'],
                ['creationDate' => '2024-01-01 12:34:04', 'lastUpdateDate' => '2024-01-31 02:53:45'],
            );

        $result = $this->widgetManager->getWidgetS3ResourcesStatus($widget, $brand, $country, $languages, $referenceLanguage);
        $resultWithNoBrand = $this->widgetManager->getWidgetS3ResourcesStatus($widget, null, $country, $languages, $referenceLanguage);

        $expected = [
            "APP" => [
                "en" => [
                    "filename" => "app/testbrand/en-TESTCOUNTRY/testwidget/widget-settings.json",
                    "url" => "testBucket/app/testbrand/en-TESTCOUNTRY/testwidget/widget-settings.json",
                    "creationDate" => "2024-01-01 12:34:04",
                    "lastModified" => "2024-01-31 02:53:45"
                ],
                "fr" => [
                    "filename" => "app/testbrand/fr-TESTCOUNTRY/testwidget/widget-settings.json",
                    "url" => "testBucket/app/testbrand/fr-TESTCOUNTRY/testwidget/widget-settings.json",
                    "creationDate" => "2024-01-01 12:34:04",
                    "lastModified" => "2024-01-31 02:53:45"
                ]
            ],
            "WEB" => [
                "en" => [
                    "filename" => "web/testbrand/en-TESTCOUNTRY/testwidget/widget-settings.json",
                    "url" => "testBucket/web/testbrand/en-TESTCOUNTRY/testwidget/widget-settings.json",
                    "creationDate" => "2024-01-01 12:34:04",
                    "lastModified" => "2024-01-31 02:53:45"
                ],
                "fr" => [
                    "filename" => "web/testbrand/fr-TESTCOUNTRY/testwidget/widget-settings.json",
                    "url" => "testBucket/web/testbrand/fr-TESTCOUNTRY/testwidget/widget-settings.json",
                    "creationDate" => "2024-01-01 12:34:04",
                    "lastModified" => "2024-01-31 02:53:45"
                ]
            ]
        ];
        $this->assertEquals($expected, $result);
        $this->assertEquals([], $resultWithNoBrand);
    }

    public function testGetWidgetS3ResourcesStatusReturnEmptyFilesList(): void
    {
        $widget = $this->createMock(Widget::class);
        $widget->method('getName')->willReturn('testWidget');
        $brand = $this->createMock(Brand::class);
        $brand->method('getCode')->willReturn('testBrand');
        $country = $this->createMock(Country::class);
        $country->method('getCode')->willReturn('testCountry');
        $languageBuilder = new LanguageBuilder();
        $languages = new ArrayCollection([
            $languageBuilder->withCode('en')->build(),
        ]);
        $referenceLanguage = $this->createMock(Language::class);
        $widget->method('getFeaturesConfiguration')->willReturn(['sources' => ['WEB']]);
        $this->s3Service->expects($this->exactly(1))
            ->method('getObjectMetaData')
            ->withConsecutive(
                ['testBucket', 'web/testbrand/en-TESTCOUNTRY/testwidget/widget-settings.json'],
            )
            ->willReturnOnConsecutiveCalls(
                false,
            );
        $result = $this->widgetManager->getWidgetS3ResourcesStatus($widget, $brand, $country, $languages, $referenceLanguage);
        $expected = [
            "WEB" => [
                "en" => [
                    "filename" => "web/testbrand/en-TESTCOUNTRY/testwidget/widget-settings.json",
                    "url" => "#",
                    "creationDate" => "",
                    "lastModified" => ""
                ],
            ]
        ];
        $this->assertEquals($expected, $result);
    }

    public function testList()
    {
        $data = $this->createMock(Widget::class);
        $this->widgetRepo->expects($this->exactly(1))->method("findAll")->willReturn([$data]);
        $result = $this->widgetManager->list();
        $this->assertEquals([$data], $result);
    }

    public function testLoad()
    {
        $brand = $this->getBrand();
        $country = $this->getCountry();
        $languages = $this->createMock(Collection::class);
        $widget = $this->getWidget();
        $result = $this->widgetManager->load($widget, $brand, $country, $languages);
        $this->assertIsArray($result);
    }

    public function testSave()
    {
        $widget = $this->getWidget();
        $brand = $this->getBrand();
        $country = $this->getCountry();
        $language = $this->getLanguage();
        $languages = new ArrayCollection();
        $languages->add($language);
        $data = [
            "source" => "WEB",
            "enabled" => "",
            "code" => "roadside",
            "title" => "Roadside assistance",
            "phone" => "967679767678",
            "availability__fr" => "fr",
            "availability__en" => "en"
        ];
        $featureCode = "roadside";
        $widgetFeature = $this->widgetManager->saveFeature($widget, $featureCode, $data, $brand, $country, $languages);
        $this->assertInstanceOf(WidgetFeature::class, $widgetFeature);
    }

    public function testSaveandEditWidget()
    {
        $widget = $this->getWidget();
        $content = '{
            "labels": [
                "WA_assistance_title"
            ],
            "sources": [
                "WEB"
            ],
            "features": [
                {
                    "code": "roadside",
                    "title": "Roadside assistance",
                    "fields": [
                        {
                            "name": "phone",
                            "type": "TextType",
                            "options": {
                                "label": "Phone",
                                "required": false
                            },
                            "section": "default",
                            "translatable": false
                        },
                         {
          "name": "Cards",
          "type": "MultiFieldType",
          "options": {
            "label": "Carousel",
            "model": {
              "title": {
                "type": "TextType",
                "required": true,
                "translatable": true
              },
              "order": {
                "type": "IntegerType",
                "required": true
              },
              "imageUrl": "ImageType",
              "redirectUrl": {
                "type": "TextType",
                "required": true
                            }
                        }
          
                    },
                    "section": "default",
                    "translatable": false
                }
                    ],
                    "enabled": true
                }
            ],
            "widgetname": "brand_assistance"
        }';
        $file = new UploadedFile(
            tempnam(sys_get_temp_dir(), 'test'), // temporary file name
            'test.json', // file name
            null, // MIME type
            null, // file size
            true // is test file
        );
        $file = tempnam(sys_get_temp_dir(), 'test') . '.json'; // create a temporary file name
        file_put_contents($file, $content); // write the JSON string to the file

        $result_saveWidget = $this->widgetManager->saveWidget($file, $widget);
        $result_editWidget = $this->widgetManager->editWidget($file, $widget);
        $this->assertInstanceOf(Widget::class, $result_saveWidget);
        $this->assertInstanceOf(Widget::class, $result_editWidget);
        $widget->addWidgetFeature($this->getWidgetFeature());
        $widget->addWidgetData(($this->getWidgetData()));
        $result_editWidget = $this->widgetManager->editWidget($file, $widget);
        $this->assertInstanceOf(Widget::class, $result_editWidget);
    }

    public function testSaveWidgetException()
    {
        $widget = $this->createMock(Widget::class);
        $file = $this->createMock(UploadedFile::class);
        $widget
            ->expects($this->once())
            ->method('setWguid')
            ->willThrowException(new \Exception('Unable to Generate Wguid'));
        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with('error while saveing widget Unable to Generate Wguid');
        $result = $this->widgetManager->saveWidget($file, $widget);
        $this->assertEquals($widget, $result);
    }

    public function testPublishWidgetData()
    {
        $widgetData = $this->getWidgetData();
        $referenceLanguage = $this->getLanguage();
        $languages = new ArrayCollection();
        $this->widgetFeatureRepo
            ->expects($this->any())
            ->method('findOneBy')
            ->with(['code' => 'roadside', 'widget' => $this->getWidget(), 'brand' => $this->getBrand(), 'country' => $this->getCountry(), 'source' => 'WEB'])
            ->willReturn($this->getWidgetFeature());
        $this->s3Service->expects($this->once())->method("putObject")->willReturn(true);
        $result = $this->widgetManager->publishWidgetData($widgetData, $languages, $referenceLanguage);
        $this->assertIsBool($result);
    }

    public function testEnable()
    {
        $widget = $this->getWidget();
        $brand = $this->getBrand();
        $country = $this->getCountry();
        $source = "WEB";
        $enable = true;
        $result = $this->widgetManager->enable($widget, $brand, $country, $source, $enable);
        $this->assertInstanceOf(WidgetData::class, $result);
    }

    public function testLabelsAndWidgetEnabled()
    {
        $widget = $this->getWidget();
        $brand = $this->getBrand();
        $country = $this->getCountry();
        $source = "WEB";
        $refLanguage = $this->getLanguage();
        $localLanguage = $this->getLanguage();
        $localLanguage->setIsReference(false);
        $testLabels = $this->widgetManager->getLabels($widget, $brand, $country, $source, $localLanguage, $refLanguage);
        $testIsWidgetEnabled = $this->widgetManager->isWidgetEnabled($widget, $brand, $country, $source);
        $this->assertIsArray($testLabels);
        $this->assertEquals(false, $testIsWidgetEnabled);
    }

    public function testIsWidgetEnabled()
    {
        $widget = $this->getWidget();
        $brand = $this->getBrand();
        $country = $this->getCountry();
        $source = "WEB";
        $this->widgetDataRepo->expects($this->any())->method("findOneBy")->willReturn($this->getWidgetData());
        $testIsWidgetEnabled = $this->widgetManager->isWidgetEnabled($widget, $brand, $country, $source);
        $this->assertIsBool($testIsWidgetEnabled);
    }

    public function testSaveWidgetLabels()
    {
        $labels = ["test1", "test2"];
        $sources = ["WEB", "APP"];
        $widget = $this->getWidget();
        $this->brandRepo->expects($this->once())->method("findAll")->willReturn([$this->getBrand()]);
        $result = $this->widgetManager->saveWidgetsLabels($labels, $sources, $widget);
        $this->assertInstanceOf(Widget::class, $result);
    }

    public function testGetWidgetAndConfig()
    {
        $brand = $this->getBrand();
        $country = $this->getCountry();
        $this->widgetRepo->expects($this->any())->method("findWidgetAndConfig")->willReturn([$this->getWidget()]);
        $result = $this->widgetManager->getWidgetAndConfig($brand, $country);
        $this->assertEquals([$this->getWidget()], $result);
    }

    public function testRemoveWidgetWithNoDependences()
    {
        $widget = $this->getWidget();
        $result = $this->widgetManager->removeWidget($widget);
        $this->assertEquals(true, $result);
    }

    public function testRemoveWidgetWithDependences()
    {
        $widget = $this->getWidget();
        $widget->addWidgetData($this->getWidgetData());
        $result = $this->widgetManager->removeWidget($widget);
        $this->assertEquals(false, $result);
    }

    public function testGetFormDataWithoutEnable()
    {
        $widget = $this->getWidget();
        $brand = $this->getBrand();
        $source = "WEB";
        $country = $this->getCountry();
        $featureCode = "123";
        $feature = $this->getWidgetFeature();
        $this->widgetFeatureRepo->expects($this->any())->method('findOneBy')->willReturn($feature);
        $result = $this->widgetManager->getFormData($widget, $featureCode, $brand, $country, $source);
        $this->assertIsArray($result);
    }

    /**
     * @return void
     */
    public function testisValidWidgetFileFailed(): void
    {
        $content = 'test';
        $tmpFile = tempnam(sys_get_temp_dir(), 'test');
        file_put_contents($tmpFile, $content);
        $file = new UploadedFile($tmpFile, 'test.json');

        $result = $this->widgetManager->isValidWidgetFile($file);

        $this->assertArrayHasKey(0, $result);
    }

    public function testisValidWidgetFileSucces(): void
    {
        $content = '{
            "labels": [
                "widget_label_1",
                "widget_label_2",
                "widget_label_3",
                "widget_label_4"
            ],
            "sources": [
                "WEB",
                "APP"
            ],
            "features": [
                {
                    "code": "feature1",
                    "title": "feature 1",
                    "fields": [
                        {
                            "name": "name",
                            "type": "TextType",
                            "options": {
                                "label": "name",
                                "required": true
                            },
                            "section": "default",
                            "translatable": false
                        },
                        {
                            "name": "heroImage",
                            "type": "MediaType",
                            "options": {
                                "label": "heroImage",
                                "required": true
                            },
                            "section": "default",
                            "translatable": false
                        },
                        {
                           "name":"Multifield example",
                           "type":"MultiFieldType",
                           "options":{
                              "label":"Multifield with outline structure dev",
                              "required":true,
                              "model":{
                                 "parent":"TextType",
                                 "id":"TextType",
                                 "label":"TextType",
                                 "image":"MediaType",
                                 "url":"TextType"
                              }
                           },
                           "section":"default",
                           "translatable":true
                        }
                    ]
                },
                {
                    "code": "feature2",
                    "title": "feature 2",
                    "fields": [
                        {
                            "name": "enabledtest",
                            "type": "CheckboxType",
                            "options": {
                                "label": "enabled",
                                "required": false
                            },
                            "section": "default",
                            "translatable": false
                        },
                        {
                            "name": "apikey",
                            "type": "TextType",
                            "options": {
                                "label": "api key",
                                "required": true
                            },
                            "section": "default",
                            "translatable": false
                        }
                    ]
                }
            ]
        }';
        $tmpFile = tempnam(sys_get_temp_dir(), 'test');
        file_put_contents($tmpFile, $content);
        $file = new UploadedFile($tmpFile, 'test.json');

        $result = $this->widgetManager->isValidWidgetFile($file);

        $this->assertEquals([], $result);
    }

    public function testisValidWidgetFileFailedValidation(): void
    {
        $content = '{
            "labels": [
                "widget_label_1",
                "widget_label_2",
                "widget_label_3",
                "widget_label_4"
            ],
            "sources": [
                "WEB",
                "APP"
            ],
            "features": [
                {
                    "code": "feature1",
                    "title": "feature 1",
                    "fields": [
                        {
                            "name": "name",
                            "type": "TextType",
                            "options": {
                                "label": "name",
                                "required": true
                            },
                            "section": "default",
                            "translatable": false
                        },
                        {
                            "name": "heroImage",
                            "type": "MediaType",
                            "options": {
                                "label": "heroImage",
                                "required": true
                            },
                            "section": "default",
                            "translatable": false
                        },
                        {
                           "name":"Multifield example",
                           "type":"MultiFieldType",
                           "options":{
                              "label":"Multifield with outline structure dev",
                              "required":true,
                              "model":{
                                 "parent":"TextType",
                                 "id":"TextType",
                                 "label":"TextType",
                                 "image":"MediaType",
                                 "url":"TextType"
                              }
                           },
                           "section":"default",
                           "translatable":true
                        }
                    ]
                },
                {
                    "code": "feature2",
                    "title": "feature 2",
                    "fields": [
                        {
                            "name": "enabledtest",
                            "type": "CheckboxType",
                            "options": {
                                "label": "enabled",
                                "required": false
                            },
                            "section": "default",
                            "translatable": false
                        },
                        {
                            "name": "apikey",
                            "type": "TextType",
                            "options": {
                                "label": "api key",
                                "required": true
                            },
                            "section": "default",
                            "translatable": false
                        }
                    ]
                }
            ]
        }';

        $logger = $this->createMock(LoggerInterface::class);
        $s3Service = $this->createMock(AWSS3Service::class);
        $widgetRepo = $this->createMock(WidgetRepository::class);
        $widgetDataRepo = $this->createMock(WidgetDataRepository::class);
        $brandRepo = $this->createMock(BrandRepository::class);
        $widgetFeatureRepo = $this->createMock(WidgetFeatureRepository::class);
        $widgetFeatureAttributeRepo = $this->createMock(WidgetFeatureAttributeRepository::class);
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $unityOfWork = $this->createMock(UnitOfWork::class);
        $unityOfWork->expects($this->any())->method("getOriginalEntityData")->willReturn(['name' => 'test']);
        $entityManager->expects($this->any())->method("getUnitOfWork")->willReturn($unityOfWork);
        $brandRepo->expects($this->any())->method("findAll")->willReturn([$this->getBrand()]);
        $validator = $this->createMock(ValidatorInterface::class);
        $translationKeyFactory = $this->createMock(TranslationKeyFactory::class);
        $accessLogRepo = $this->createMock(AccessLogRepository::class);
        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            ['name'],
            null,
            'name',
            null
        );
        $validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $widgetManager = new WidgetManager(
            $entityManager,
            $widgetRepo,
            $widgetFeatureRepo,
            $widgetFeatureAttributeRepo,
            $this->createMock(LanguageRepository::class),
            $widgetDataRepo,
            $s3Service,
            $this->createMock(LabelTranslationManager::class),
            'testBucket',
            $brandRepo,
            $validator,
            $translationKeyFactory,
            $this->channelRepo,
            $this->serializer,
            $this->normalizer,
            $accessLogRepo
        );
        $widgetManager->setLogger($logger);
        $tmpFile = tempnam(sys_get_temp_dir(), 'test');
        file_put_contents($tmpFile, $content);
        $file = new UploadedFile($tmpFile, 'test.json');
        $result = $widgetManager->isValidWidgetFile($file);
        $this->assertArrayHasKey(0, $result);
    }

    public function testSaveMultifield()
    {
        $widget = $this->getWidget();
        $brand = $this->getBrand();
        $country = $this->getCountry();
        $language = $this->getLanguage();
        $languages = new ArrayCollection();
        $languages->add($language);
        $data = [
            "source" => "WEB",
            "enabled" => "",
            "code" => "multifield",
            "title" => "Multifields feature",
            "multifields" => [
                [
                    "type" => "isMultifield",
                    "label" => ""
                ]
            ]
        ];
        $featureCode = "multifieldFeature";
        $widgetFeature = $this->widgetManager->saveFeature($widget, $featureCode, $data, $brand, $country, $languages);
        $this->assertInstanceOf(WidgetFeature::class, $widgetFeature);
    }

    public function testPublishWidgetDataMultifield()
    {
        $widgetData = $this->getWidgetData();
        $referenceLanguage = $this->getLanguage();
        $languages = new ArrayCollection();
        $this->widgetFeatureRepo
            ->expects($this->any())
            ->method('findOneBy')
            ->willReturn($this->getWidgetFeature());
        $this->s3Service->expects($this->once())->method("putObject")->willReturn(true);
        $result = $this->widgetManager->publishWidgetData($widgetData, $languages, $referenceLanguage);
        $this->assertIsBool($result);
    }

    public function testMakePackage()
    {
        $widget = $this->getWidget();
        $this->widgetRepo->expects($this->once())->method("findBy")->willReturn([$widget]);
        $json = $this->widgetManager->makePackage($widget);
        $this->assertIsString($json);
    }

    public function testImportWidget()
    {
        $jsonContent = '{
            "name": "brand_assistance_test_new",
            "description": "Brand and Roadside Assistance widget new",
            "version": "1.1",
            "type": "space",
            "widgetFeatures": [
                
            ],
            "widgetData": [
                
            ],
            "featuresConfiguration": {
                "sources": ["WEB", "APP"],
                "features": [
                    {
                        "code": "roadside",
                        "title": "Roadside assistance",
                        "fields": [
                            {
                                "name": "phone",
                                "type": "TextType",
                                "options": { "label": "Phone", "required": false },
                                "section": "default",
                                "translatable": false
                            },
                            {
                                "name": "availability",
                                "type": "TextType",
                                "options": { "label": "availability", "required": false },
                                "section": "default",
                                "translatable": true
                            }
                        ],
                        "enabled": true
                    },
                    {
                        "code": "brand",
                        "title": "Brand assistance",
                        "fields": [
                            {
                                "name": "faqurl",
                                "type": "TextType",
                                "options": { "label": "Faq url", "required": false },
                                "section": "Online contact",
                                "translatable": true
                            },
                            {
                                "name": "contactformurl",
                                "type": "TextType",
                                "options": { "label": "Contact form url", "required": false },
                                "section": "Online contact",
                                "translatable": true
                            },
                            {
                                "name": "phone",
                                "type": "TextType",
                                "options": { "label": "phone", "required": false },
                                "section": "Phone contact",
                                "translatable": false
                            }
                        ],
                        "enabled": true
                    },
                    {
                        "code": "dealer",
                        "title": "My dealer",
                        "fields": [
                            {
                                "name": "searchurl",
                                "type": "TextType",
                                "options": { "label": "Search dealer url", "required": false },
                                "section": "default",
                                "translatable": false
                            }
                        ],
                        "enabled": true
                    }
                ],
                "widgetname": "brand_assistance"
            }
        }';


        [$status, $message] = $this->widgetManager->importWidget($jsonContent);
        $this->assertEquals(true, $status);
        $this->assertEquals("Widget imported successfully", $message);
    }

    public function testImportWidgetWithException()
    {
        $jsonContent = '{
            "name": "brand_assistance_test_new",
            "description": "Brand and Roadside Assistance widget new",
            "version": "1.1",
            "type": "space",
            "wguid":""
            "widgetFeatures": [
                
            ],
            "widgetData": [
                
            ],
            "featuresConfiguration": {
                "sources": ["WEB", "APP"],
                "features": [
                    {
                        "code": "roadside",
                        "title": "Roadside assistance",
                        "fields": [
                            {
                                "name": "phone",
                                "type": "TextType",
                                "options": { "label": "Phone", "required": false },
                                "section": "default",
                                "translatable": false
                            },
                            {
                                "name": "availability",
                                "type": "TextType",
                                "options": { "label": "availability", "required": false },
                                "section": "default",
                                "translatable": true
                            }
                        ],
                        "enabled": true
                    },
                    {
                        "code": "brand",
                        "title": "Brand assistance",
                        "fields": [
                            {
                                "name": "faqurl",
                                "type": "TextType",
                                "options": { "label": "Faq url", "required": false },
                                "section": "Online contact",
                                "translatable": true
                            },
                            {
                                "name": "contactformurl",
                                "type": "TextType",
                                "options": { "label": "Contact form url", "required": false },
                                "section": "Online contact",
                                "translatable": true
                            },
                            {
                                "name": "phone",
                                "type": "TextType",
                                "options": { "label": "phone", "required": false },
                                "section": "Phone contact",
                                "translatable": false
                            }
                        ],
                        "enabled": true
                    },
                    {
                        "code": "dealer",
                        "title": "My dealer",
                        "fields": [
                            {
                                "name": "searchurl",
                                "type": "TextType",
                                "options": { "label": "Search dealer url", "required": false },
                                "section": "default",
                                "translatable": false
                            }
                        ],
                        "enabled": true
                    }
                ],
                "widgetname": "brand_assistance"
            }
        }';

        [$status, $message] = $this->widgetManager->importWidget($jsonContent);
        $this->assertEquals(false, $status);
    }

    public function testGetAccessLog()
    {
        $accessLog = new AccessLog();
        $date = $this->createMock(DateTime::class);
        $accessLog->setTimestamp($date);
        $accessLog->setUsername("admin");
        $accessLog->setValuesBefore(["value" => 123]);
        $accessLog->setValuesAfter(["value" => 1234]);
        $accessLogs = [
            'results' => [
                [
                    0 => $accessLog,
                    'featureName' => 'testFeature',
                    'label' => "English",
                    'name' => 'code',
                    'channel' => "WEB"
                ],
            ]
        ];
        $widget = $this->getWidget();
        $brand = $this->getBrand();
        $country = $this->getCountry();
        $this->accessLogRepository->expects($this->once())->method("getWidgetLogs")->willReturn($accessLogs);
        $response = $this->widgetManager->getAccessLog($widget, $brand, $country);
        $this->assertIsArray($response);
        $this->assertEquals($response['recordsFiltered'], 1);
        $this->assertEquals($response['recordsTotal'], 1);
    }
}
