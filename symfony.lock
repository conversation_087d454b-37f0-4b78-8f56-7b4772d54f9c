{"doctrine/annotations": {"version": "1.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/doctrine-bundle": {"version": "2.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "013b823e7fee65890b23e40f31e6667a1ac519ac"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "drenso/symfony-oidc-bundle": {"version": "2.9", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.0", "ref": "e2b975158d940a191f48e3ff2c59108a1d7225e6"}}, "friendsofphp/php-cs-fixer": {"version": "3.17", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "hautelook/alice-bundle": {"version": "2.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.2", "ref": "c84e4f2b9d7f436d7d52e8369230b393367607ec"}, "files": ["config/packages/hautelook_alice.yaml", "fixtures/.gitignore"]}, "jms/serializer-bundle": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "cc04e10cf7171525b50c18b36004edf64cb478be"}, "files": ["config/packages/jms_serializer.yaml"]}, "nelmio/alice": {"version": "3.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "42b52d2065dc3fde27912d502c18ca1926e35ae2"}, "files": ["config/packages/nelmio_alice.yaml"]}, "phpstan/phpstan": {"version": "1.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "d74d4d719d5f53856c9c13544aa22d44144b1819"}, "files": ["phpstan.neon"]}, "phpunit/phpunit": {"version": "9.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.3", "ref": "a6249a6c4392e9169b87abf93225f7f9f59025e6"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/console": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["bin/console"]}, "symfony/debug-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "3cd216a4d007b78d8554d44a5b1c0a446dab24fb"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/mailer": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "2bf89438209656b85b9a49238c4467bff1b1f939"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.48", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "dfe610928a5c61619bdfc830cd7fa7f091368023"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "213676c4ec929f046dfde5ea8e97625b81bc0578"}, "files": ["config/packages/monolog.yaml"]}, "symfony/notifier": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.0", "ref": "c31585e252b32fe0e1f30b1f256af553f4a06eb9"}, "files": ["config/packages/notifier.yaml"]}, "symfony/phpunit-bridge": {"version": "6.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "819d3d2ffa4590eba0b8f4f3e5e89415ee4e45c3"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/routing": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "85de1d8ae45b284c3c84b668171d2615049e698f"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "98f1f2b0d635908c2b40f3675da2d23b1a069d30"}, "files": ["config/packages/security.yaml"]}, "symfony/translation": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "da64f5a2b6d96f5dc24914517c0350a5f91dee43"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "bb2178c57eee79e6be0b297aa96fc0c0def81387"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "d294ad4add3e15d7eb1bae0221588ca89b38e558"}, "files": ["config/packages/uid.yaml"]}, "symfony/validator": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "24bbc3d84ef2f427f82104f766014e799eefcc3e"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/webapp-pack": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "2fb3513dbc139884fc5b7c751242b66f9f10f0c3"}, "files": ["config/packages/messenger.yaml"]}, "theofidry/alice-data-fixtures": {"version": "1.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fe5a50faf580eb58f08ada2abe8afbd2d4941e05"}}, "twig/extra-bundle": {"version": "v3.5.0"}}