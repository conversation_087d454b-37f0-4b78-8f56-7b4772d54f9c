function doPromiseAjax(url, method, data = {}, dataType = "json") {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: url,
            type: method,
            dataType: dataType,
            data: data,
            success: function (result) {
                return resolve(result);
            },
            error: function (xhr, status, error) {
                return reject({ status, error, xhr });
            }
        });
    });
}

export { doPromiseAjax };

