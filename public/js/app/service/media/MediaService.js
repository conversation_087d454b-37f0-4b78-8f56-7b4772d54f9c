import { doPromiseAjax } from '../../connector/AjaxConnector.js';

function deleteMediaById(id, search = '') {
    let url = window.deleteMediaUrl.replace(':id', id);
    let data = $("form#delete-media-form").serialize() + '&search=' + search;
    return doPromiseAjax(url, "DELETE", data);
}

function getMediaInfo(id) {
    let url = window.getMediaDetailPath.replace(':id', id);
    return doPromiseAjax(url, "GET");
}

function searchCall(id, search) {
    let url = window.getSearchedMedia.replace(':id', id);
    return doPromiseAjax(url, "GET", {search: search});
}

export { deleteMediaById, getMediaInfo, searchCall };

