import { doPromiseAjax } from '../../connector/AjaxConnector.js';

function getDirectoryImages(id, currentPage, search = '') {
    let url = window.mediaDirectoryUrl.replace(':id', id);
    let data = {
        page: currentPage,
        search: search
    };
    return doPromiseAjax(url, "GET", data);
}

function saveNewFolder(id, input) {
    let url = window.mediaDirectoryAddUrl.replace(':id', id);
    let data = {
        "directory": input
    };
    return doPromiseAjax(url, "POST", data);
}

function saveUpdateFolder(id, input) {
    let url = window.mediaDirectoryEditUrl.replace(':id', id);
    let data = {
        "directory": input
    };
    return doPromiseAjax(url, "POST", data);
}

function deleteFolderById(id) {
    let url = window.mediaDirectoryRemoveUrl.replace(':id', id);
    return doPromiseAjax(url, "DELETE");
}

export { deleteFolderById, getDirectoryImages, saveNewFolder, saveUpdateFolder };
