!function(){var e={strings:{addMoreFiles:"Ajouter d'autres fichiers",addingMoreFiles:"En train d'ajouter des fichiers",allowAccessDescription:"Pour prendre des photos ou enregistrer une vid\xe9o avec votre cam\xe9ra, veuillez autoriser l'acc\xe8s \xe0 votre cam\xe9ra pour ce site.",allowAccessTitle:"Veuillez autoriser l'acc\xe8s \xe0 votre cam\xe9ra",authenticateWith:"Se connecter \xe0 %{pluginName}",authenticateWithTitle:"Veuillez vous authentifier avec %{pluginName} pour s\xe9lectionner les fichiers",back:"Retour",addMore:"Ajouter d'autres",browse:"naviguer",cancel:"Annuler",cancelUpload:"Annuler t\xe9l\xe9chargement",chooseFiles:"Choisir des fichiers",closeModal:"Ferm<PERSON> fen\xeatre",companionAuthError:"Autorisation requise",companionError:"Connexion avec Companion a \xe9chou\xe9",complete:"Termin\xe9",connectedToInternet:"Connect\xe9 \xe0 Internet",copyLink:"Copier lien",copyLinkToClipboardFallback:"Copier le lien en dessous",copyLinkToClipboardSuccess:"Lien copi\xe9",creatingAssembly:"Pr\xe9paration du t\xe9l\xe9chargement...",creatingAssemblyFailed:"Transloadit: Impossible de cr\xe9er une Assembly",dashboardTitle:"T\xe9l\xe9chargeur de fichier",dashboardWindowTitle:"Fen\xeatre de t\xe9l\xe9chargeur de fichier (Appuyez sur echap pour fermer)",dataUploadedOfTotal:"%{complete} sur %{total}",done:"Termin\xe9",dropHereOr:"D\xe9posez les fichiers ici ou %{browse}",dropHint:"D\xe9posez vos fichiers ici",dropPaste:"D\xe9posez les fichiers ici ou %{browse}",dropPasteImport:"D\xe9posez les fichiers ici, coller, %{browse} ou importer du",edit:"Modifier",editFile:"Modifier fichier",editing:"Modification en cours du %{file}",emptyFolderAdded:"Aucun fichier n'a \xe9t\xe9 ajout\xe9 depuis un dossier vide",encoding:"Traitement...",enterCorrectUrl:"Lien incorrecte: Assurez-vous que vous entrez un lien direct vers le fichier",enterUrlToImport:"Entrez un lien ou importer un fichier",exceedsSize:"Ce fichier d\xe9passe la taille maximale autoris\xe9e de",failedToFetch:"Companion a \xe9chou\xe9 \xe0 r\xe9cup\xe9rer ce lien, assurez-vous que c'est correct",failedToUpload:"Le t\xe9l\xe9chargement de %{file} a \xe9chou\xe9",fileSource:"Fichier source: %{name}",filesUploadedOfTotal:{0:"%{complete} sur %{smart_count} fichier t\xe9l\xe9charg\xe9",1:"%{complete} sur %{smart_count} fichiers t\xe9l\xe9charg\xe9s",2:"%{complete} sur %{smart_count} fichiers t\xe9l\xe9charg\xe9s"},filter:"Filtrer",finishEditingFile:"Terminer l'\xe9dition du fichier",folderAdded:{0:"%{smart_count} ajout\xe9 fichier de %{folder}",1:"%{smart_count} ajout\xe9 fichiers de %{folder}",2:"%{smart_count} ajout\xe9 fichiers de %{folder}"},import:"Importer",importFrom:"Importer du %{name}",link:"Lien",loading:"Chargement...",logOut:"D\xe9connexion",myDevice:"Mon appareil",noFilesFound:"Vous n'avez aucun fichier ou dossier ici",noInternetConnection:"Pas de connexion \xe0 Internet",pause:"Pause",pauseUpload:"Mettre en pause le t\xe9l\xe9chargement",paused:"En pause",poweredBy:"Soutenu par",preparingUpload:"Pr\xe9paration du t\xe9l\xe9chargement...",processingXFiles:{0:"Traitement de %{smart_count} fichier",1:"Traitement de %{smart_count} fichiers",2:"Traitement de %{smart_count} fichiers"},recordingLength:"Dur\xe9e d'enregistrement %{recording_length}",removeFile:"Effacer le fichier",resetFilter:"R\xe9initialiser filtre",resume:"Continuer",resumeUpload:"Continuer le t\xe9l\xe9chargement",retry:"R\xe9essayer",retryUpload:"R\xe9essayez le t\xe9l\xe9chargement",saveChanges:"Sauvegarder les modifications",selectX:{0:"S\xe9lectionner %{smart_count}",1:"S\xe9lectionner %{smart_count}",2:"S\xe9lectionner %{smart_count}"},smile:"Souris!",startRecording:"Commencer l'enregistrement vid\xe9o",stopRecording:"Arr\xeater l'enregistrement vid\xe9o",takePicture:"Prendre une photo",timedOut:"T\xe9l\xe9chargement bloqu\xe9 pour %{seconds} secondes, annulation.",upload:"T\xe9l\xe9charger",uploadComplete:"T\xe9l\xe9chargement termin\xe9",uploadFailed:"T\xe9l\xe9chargement a \xe9chou\xe9",uploadPaused:"T\xe9l\xe9chargement mis en pause",uploadXFiles:{0:"T\xe9l\xe9charger %{smart_count} fichier",1:"T\xe9l\xe9charger %{smart_count} fichiers",2:"T\xe9l\xe9charger %{smart_count} fichiers"},uploadXNewFiles:{0:"T\xe9l\xe9charger +%{smart_count} fichier",1:"T\xe9l\xe9charger +%{smart_count} fichiers",2:"T\xe9l\xe9charger +%{smart_count} fichiers"},uploading:"T\xe9l\xe9chargement en cours",uploadingXFiles:{0:"Uploading %{smart_count} file",1:"Uploading %{smart_count} files",2:"Uploading %{smart_count} files"},xFilesSelected:{0:"%{smart_count} fichier s\xe9lectionn\xe9",1:"%{smart_count} fichiers s\xe9lectionn\xe9s",2:"%{smart_count} fichiers s\xe9lectionn\xe9s"},xMoreFilesAdded:{0:"%{smart_count} autre fichier ajout\xe9",1:"%{smart_count} autres fichiers ajout\xe9s",2:"%{smart_count} autres fichiers ajout\xe9s"},xTimeLeft:"%{time} restantes",youCanOnlyUploadFileTypes:"Vous pouvez seulement t\xe9l\xe9charger: %{types}",youCanOnlyUploadX:{0:"Vous pouvez seulement t\xe9l\xe9charger %{smart_count} fichier",1:"Vous pouvez seulement t\xe9l\xe9charger %{smart_count} fichiers",2:"Vous pouvez seulement t\xe9l\xe9charger %{smart_count} fichiers"},youHaveToAtLeastSelectX:{0:"Vous devez s\xe9lectionner au moins %{smart_count} fichier",1:"Vous devez s\xe9lectionner au moins %{smart_count} fichiers",2:"Vous devez s\xe9lectionner au moins %{smart_count} fichiers"},selectAllFilesFromFolderNamed:"S\xe9lectionner tous les fichiers du dossier %{name}",unselectAllFilesFromFolderNamed:"D\xe9s\xe9lectionner tous les fichiers du dossier%{name}",selectFileNamed:"Choisir le dossier %{name}",unselectFileNamed:"D\xe9s\xe9lectionner le fichier %{name}",openFolderNamed:"Dossier ouvert %{name}"},pluralize:function(e){return 1===e?0:1}};"undefined"!=typeof window&&void 0!==window.Uppy&&(window.Uppy.locales.fr_FR=e)}();
