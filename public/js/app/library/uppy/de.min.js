(()=>{var e={pluralize(n){return n===1?0:1}};e.strings={addBulkFilesFailed:{0:"Das Hinzuf\xFCgen einer Datei ist aufgrund eines internen Fehlers fehlgeschlagen",1:"<PERSON> Hinzuf\xFCgen von %{smart_count} Dateien ist aufgrund eines internen Fehlers fehlgeschlagen"},addingMoreFiles:"Dateien hinzuf\xFCgen",addMore:"Mehr hinzuf\xFCgen",addMoreFiles:"Dateien hinzuf\xFCgen",allFilesFromFolderNamed:"Alle Dateien vom Ordner %{name}",allowAccessDescription:"Um Bilder oder Videos mit Ihrer Kamera aufzunehmen, erlauben Sie dieser Website bitte den Zugriff auf Ihre Kamera.",allowAccessTitle:"Bitte erlauben Sie Zugriff auf Ihre Kamera",aspectRatioLandscape:"Zuschneiden auf Querformat (16:9)",aspectRatioPortrait:"Zuschneiden auf Hochformat (9:16)",aspectRatioSquare:"Zuschneiden auf Quadrat",authenticateWith:"Mit %{pluginName} verbinden",authenticateWithTitle:"Bitte authentifizieren Sie sich mit %{pluginName}, um Dateien auszuw\xE4hlen",back:"Zur\xFCck",backToSearch:"Zur\xFCck zur Suche",browse:"durchsuchen",browseFiles:"Dateien durchsuchen",browseFolders:"Ordner durchsuchen",cancel:"Abbrechen",cancelUpload:"Hochladen abbrechen",chooseFiles:"Dateien ausw\xE4hlen",closeModal:"Fenster schlie\xDFen",companionError:"Verbindung zu Companion fehlgeschlagen",companionUnauthorizeHint:"Um die Autorisierung f\xFCr Ihr %{provider} Konto aufzuheben, gehen Sie bitte zu %{url}",complete:"Fertig",connectedToInternet:"Mit dem Internet verbunden",copyLink:"Link kopieren",copyLinkToClipboardFallback:"Untenstehende URL kopieren",copyLinkToClipboardSuccess:"Link in die Zwischenablage kopiert",creatingAssembly:"Das Hochladen wird vorbereiten...",creatingAssemblyFailed:"Transloadit: Assembly konnte nicht erstellt werden",dashboardTitle:"Hochladen von Dateien",dashboardWindowTitle:"Hochladen von Dateien (ESC dr\xFCcken zum Schlie\xDFen)",dataUploadedOfTotal:"%{complete} von %{total}",discardRecordedFile:"Aufgenommene Datei verwerfen",done:"Abgeschlossen",dropHereOr:"Dateien hier ablegen oder %{browse}",dropHint:"Dateien hier ablegen",dropPasteBoth:"Dateien hier ablegen/einf\xFCgen, %{browseFiles} oder %{browseFolders}",dropPasteFiles:"Dateien hier ablegen/einf\xFCgen oder %{browseFiles}",dropPasteFolders:"Dateien hier ablegen/einf\xFCgen oder %{browseFolders}",dropPasteImportBoth:"Dateien hier ablegen/einf\xFCgen, %{browse} oder von folgenden Quellen importieren:",dropPasteImportFiles:"Dateien hier ablegen/einf\xFCgen, %{browseFiles} oder von folgenden Quellen importieren:",dropPasteImportFolders:"Dateien hier ablegen/einf\xFCgen, %{browseFolders} oder von folgenden Quellen importieren:",editFile:"Datei bearbeiten",editFileWithFilename:"Datei %{file} bearbeiten",editing:"%{file} bearbeiten",emptyFolderAdded:"Keine Dateien hinzugef\xFCgt, da der Ordner leer war",encoding:"Kodieren...",enterCorrectUrl:"Falsche URL: Bitte stellen Sie sicher, dass Sie einen direkten Link zu einer Datei eingeben",enterTextToSearch:"Text zum Suchen von Bildern eingeben",enterUrlToImport:"URL zum Importieren einer Datei eingeben",exceedsSize:"Datei %{file} ist gr\xF6\xDFer als die maximal erlaubte Dateigr\xF6\xDFe von %{size}",failedToFetch:"Companion konnte diese URL nicht verarbeiten - stellen Sie bitte sicher, dass sie korrekt ist",failedToUpload:"Fehler beim Hochladen der Datei %{file}",filesUploadedOfTotal:{0:"%{complete} von %{smart_count} Datei hochgeladen",1:"%{complete} von %{smart_count} Dateien hochgeladen"},filter:"Filter",finishEditingFile:"Bearbeitung beenden",flipHorizontal:"Horizontal spiegeln",folderAdded:{0:"Eine Datei von %{folder} hinzugef\xFCgt",1:"%{smart_count} Dateien von %{folder} hinzugef\xFCgt"},folderAlreadyAdded:'Der Ordner "%{folder}" wurde bereits hinzugef\xFCgt',generatingThumbnails:"Erstellen von Miniaturansichten...",import:"Importieren",importFiles:"Importiere Dateien von:",importFrom:"Importieren von %{name}",inferiorSize:"Diese Datei ist kleiner als die minimal erlaubte Dateigr\xF6\xDFe von %{size}",loading:"Laden...",logOut:"Abmelden",micDisabled:"Zugriff auf Mikrofon von Benutzer abgelehnt",missingRequiredMetaField:"Fehlende erforderliche Meta-Felder",missingRequiredMetaFieldOnFile:"Fehlende erforderliche Meta-Felder in %{fileName}",missingRequiredMetaFields:{0:"Fehlendes erforderliches Meta-Feld: %{fields}.",1:"Fehlende erforderliche Meta-Felder: %{fields}."},myDevice:"Mein Ger\xE4t",noCameraDescription:"Bitte Kamera anschlie\xDFen, um Bilder oder Videos aufzunehmen",noCameraTitle:"Kamera nicht verf\xFCgbar",noDuplicates:"Datei '%{fileName}' existiert bereits und kann nicht erneut hinzugef\xFCgt werden",noFilesFound:"Sie haben hier keine Dateien oder Ordner",noInternetConnection:"Keine Internetverbindung",noMoreFilesAllowed:"W\xE4hrend der Upload l\xE4uft, k\xF6nnen keine weiteren Dateien hinzugef\xFCgt werden",openFolderNamed:"Ordner %{name} \xF6ffnen",pause:"Pausieren",paused:"Pausiert",pauseUpload:"Hochladen pausieren",pluginNameBox:"Box",pluginNameCamera:"Kamera",pluginNameDropbox:"Dropbox",pluginNameFacebook:"Facebook",pluginNameGoogleDrive:"Google Drive",pluginNameInstagram:"Instagram",pluginNameOneDrive:"OneDrive",pluginNameZoom:"Zoom",poweredBy:"Powered by %{uppy}",processingXFiles:{0:"Eine Datei verarbeiten",1:"%{smart_count} Dateien verarbeiten"},recording:"Aufnahme",recordingLength:"Aufnahmedauer %{recording_length}",recordingStoppedMaxSize:"Die Aufnahme wurde gestoppt, weil die Dateigr\xF6\xDFe das Limit \xFCberschritten hat",recoveredAllFiles:"Wir haben alle Dateien wiederhergestellt. Sie k\xF6nnen mit dem Hochladen fortfahren.",recoveredXFiles:{0:"Wir konnten eine Datei nicht vollst\xE4ndig wiederherstellen. Bitte w\xE4hlen Sie sie erneut aus und fahren Sie dann mit dem Hochladen fort.",1:"Wir konnten %{smart_count} Dateien nicht vollst\xE4ndig wiederherstellen. Bitte w\xE4hlen Sie sie erneut aus und fahren Sie dann mit dem Hochladen fort."},removeFile:"Datei entfernen",reSelect:"Erneut ausw\xE4hlen",resetFilter:"Filter zur\xFCcksetzen",resume:"Fortsetzen",resumeUpload:"Hochladen fortsetzen",retry:"Erneut versuchen",retryUpload:"Hochladen erneut versuchen",revert:"R\xFCckg\xE4ngig machen",rotate:"Drehen",save:"Speichern",saveChanges:"\xC4nderungen speichern",searchImages:"Suche nach Bildern",selectX:{0:"W\xE4hlen Sie %{smart_count}",1:"W\xE4hlen Sie %{smart_count}"},sessionRestored:"",smile:"Bitte l\xE4cheln!",startCapturing:"Bildschirmaufnahme starten",startRecording:"Videoaufnahme starten",stopCapturing:"Bildschirmaufnahme stoppen",stopRecording:"Videoaufnahme stoppen",streamActive:"Stream aktiv",streamPassive:"Stream passiv",submitRecordedFile:"Aufgezeichnete Datei verwenden",takePicture:"Ein Foto machen",timedOut:"Upload f\xFCr %{seconds} Sekunden stehen geblieben, breche ab.",upload:"Hochladen",uploadComplete:"Hochladen abgeschlossen",uploadFailed:"Hochladen fehlgeschlagen",uploading:"Wird hochgeladen",uploadingXFiles:{0:"Eine Datei wird hochgeladen",1:"%{smart_count} Dateien werden hochgeladen"},uploadPaused:"Hochladen pausiert",uploadXFiles:{0:"Eine Datei hochladen",1:"%{smart_count} Dateien hochladen"},uploadXNewFiles:{0:"+%{smart_count} Datei hochladen",1:"+%{smart_count} Dateien hochladen"},xFilesSelected:{0:"Eine Datei ausgew\xE4hlt",1:"%{smart_count} Dateien ausgew\xE4hlt"},xMoreFilesAdded:{0:"Eine weitere Datei hinzugef\xFCgt",1:"%{smart_count} weitere Dateien hinzugef\xFCgt"},xTimeLeft:"%{time} verbleibend",youCanOnlyUploadFileTypes:"Sie k\xF6nnen nur folgende Dateitypen hochladen: %{types}",youCanOnlyUploadX:{0:"Sie k\xF6nnen nur eine Datei hochladen",1:"Sie k\xF6nnen nur %{smart_count} Dateien hochladen"},youHaveToAtLeastSelectX:{0:"Sie m\xFCssen mindestens eine Datei ausw\xE4hlen",1:"Sie m\xFCssen mindestens %{smart_count} Dateien ausw\xE4hlen"},zoomIn:"Vergr\xF6\xDFern",zoomOut:"Verkleinern"};typeof Uppy<"u"&&(globalThis.Uppy.locales.de_DE=e);var i=e;})();
//# sourceMappingURL=de_DE.min.js.map