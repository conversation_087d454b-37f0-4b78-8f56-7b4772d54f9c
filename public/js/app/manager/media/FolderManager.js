import {
    appendClass,
    clearInputs,
    disableSubmitBtn,
    enableSubmitBtn,
    errorAction,
    hideAfterClose,
    initiateElement,
    manageHeaderListMedia,
    showAfterClose,
    showBlocLoading,
    successAction,
    updateRoute
} from '../../helpers/MediaHelper.js';
import { createNewNode, getRoute, getTree, removeNode, updateNode } from '../../plugins/media/jstree.js';
import { deleteFolderById, saveNewFolder, saveUpdateFolder } from '../../service/media/FolderService.js';
import { getImages } from '../media/MediaManager.js';

async function showFiles(data, route, isActionsBtn) {
    let id = data.id;
    window.currentElement = 'listImages';
    clearInputs();
    initiateElement();
    manageHeaderListMedia(route, isActionsBtn);
    await getImages(id);
}

function showAddFolder() {
    window.currentElement = 'addFolder';
    initiateElement();
    clearInputs();
}

function showEditFolder(data) {
    window.currentElement = 'editFolder';
    initiateElement();
    $("#edit_input").val(data);
}

function showDeleteFolder() {
    window.currentElement = 'deleteFolder';
    initiateElement();
}

async function addNewFolder(id, input) {
    showBlocLoading();
    disableSubmitBtn();
    await saveNewFolder(id, input)
        .then(data => {
            successAction(window.folderAddedTrans);
            let node = getTree().get_node(id);
            let channelType = node.original.channelType;
            createNewNode(id, data.id, input, channelType);
            setTimeout(() => {
                enableSubmitBtn();
            }, 2000);

        })
        .catch((e) => {
            let message = window.folderAddedErrorTrans;
            if(e.xhr.status == 422) {
                message = window.invalidFolderNameErrorTrans;
            }
            errorAction(message);
            enableSubmitBtn();
        });
}

async function updateFolder(id, input) {
    showBlocLoading();
    disableSubmitBtn();
    await saveUpdateFolder(id, input)
        .then(() => {
            successAction(window.folderEditedTrans);
            updateNode(id, input);
            updateRoute(getRoute(id));
            setTimeout(() => {
                enableSubmitBtn();
                getImages(id);
            }, 2000);
            

        })
        .catch((e) => {
            let message = window.folderEditedErrorTrans;
            if(e.xhr.status == 422) {
                message = window.invalidFolderNameErrorTrans;
            }
            errorAction(message);
            enableSubmitBtn();
        });
}

async function removeFolder(id) {
    showBlocLoading();
    disableSubmitBtn();
    await deleteFolderById(id)
        .then(() => {
            successAction(window.folderDeletedTrans);
            removeNode(id);
            appendClass();
            hideAfterClose();
            showAfterClose();
            setTimeout(() => {
                enableSubmitBtn();
            }, 2000);

        })
        .catch(() => {
            errorAction(window.folderDeletedErrorTrans);
            enableSubmitBtn();
        });
}


export {
    addNewFolder,
    removeFolder,
    showAddFolder,
    showDeleteFolder,
    showEditFolder,
    showFiles,
    updateFolder
};

