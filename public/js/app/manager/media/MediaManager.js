import { hideModal, showModal } from '../../helpers/AnimationHelper.js';
import {
    disableSubmitBtn,
    enableSubmitBtn,
    getAppendClass,
    getElement,
    hideAfterClose,
    hideBlocLoading,
    initiateElementWithoutShow,
    noImages,
    showBlocLoading,
    showErrorAlertMessage,
    showSuccessAlertMessage
} from '../../helpers/MediaHelper.js';
import bytesToSize from '../../helpers/SizeConverter.js';
import { getRoute, getSelectedNode, getSelectedNodeId } from '../../plugins/media/jstree.js';
import { getPagination } from '../../plugins/media/pagination.js';
import { addNewFile, mountNewInstance, setNumberFiles } from '../../plugins/media/uppy.js';
import { getDirectoryImages } from '../../service/media/FolderService.js';
import { deleteMediaById, getMediaInfo } from '../../service/media/MediaService.js';
import {
    csvIconeSource,
    documentIconeSource,
    jsonIconeSource,
    pdfIconeSource,
    txtIconeSource,
    videoIconeSource,
    xmlIconeSource
} from '../../utils/Image.js';
import { selectedMediaButtons } from '../../utils/media/MediaActions.js';
import { formatTypes } from '../../utils/media/MediaFormatType.js';

let debounceTimer;

async function getImages(id, currentPage = 1, refreshPages = true, defaultFunction = 'getImage', search = '', currentElement = 'listImages') {
    window.currentElement = currentElement;
    showBlocLoading();
    if (refreshPages) {
        $('.page-number').remove();
        $('.page-text').hide();
    }
    let data = await getDirectoryImages(id, currentPage, search).then((d) => d);
    window.mediaList = data;
    showImages(data.success, id, currentPage, search);
}

function appendImage(file) {
    let source = getSourceIcon(file.extension.toLowerCase(), file.path);
    let name = file.path.split('/').pop();
    return `
    <div class="col-lg-3 col-xl-2" data-toggle="tooltip" data-placement="top" title="${name}">
        <div class="file-man-box selected-image" data-id="${file.id}" data-role='${file.role}' data-filename="${name}" data-path="${file.path}" data-extension="${file.extension}">
            <div class="file-img-box"><img src="${source}?no=${Math.random()}" class="image-src" data-src="${source}" alt="${name}" data-path="${file.path}"></div>
            <a href="#show-media-modal" class="file-download">
                <i class="fa fa-search-plus"></i>
            </a>
            <div class="file-man-title">
                <h5 class="mb-0 text-overflow">${name}</h5>
                <p class="mb-0">
                    <small>${bytesToSize(file.size, 2, '0 O')}</small>
                </p>
            </div>
        </div>
    </div>
     `;
}

function showImages(data, id, currentPage, search = '') {
    let images = data.medias;
    let totalPages = data.totalPages;
    let total = data.medias.length;
    let element = getElement();
    $(element['totalFiles']).text(total);
    hideBlocLoading();
    if (images.length > 0) {        
        let imageHtml = "";
        $(getAppendClass()).html('');
        for (let image of images) {
            imageHtml += appendImage(image);
        }
        $(getAppendClass()).html(imageHtml);
        getPagination(id, totalPages, currentPage, 'getImage', search);
        if(element['contentClass']) $(element['contentClass']).removeClass('d-none');
    }
    else {
        noImages(search);
    }
}

function uploadSuccess(title) {
    hideBlocLoading();
    showSuccessAlertMessage(title);
    hideAfterClose();
    let element = getElement();
    hideModal(element['closeElement']);
}

function uploadError(title) {
    hideBlocLoading();
    showErrorAlertMessage(title);
    hideAfterClose();
}

async function showEditMedia(id) {
    showBlocLoading();
    let element = getElement();
    let uppyContainerEdit = "#uppy-container-edit";
    $(uppyContainerEdit).html('');
    showModal(element['closeElement']);
    getMediaInfo(id).then((data) => {
        var media = data.success
        if (media) {
            $("#edit_medias_form_id").val(media.id);
            $(".edit_medias_form_name").val(media.name);
            $(".edit_medias_form_textAlt").val(media.textAlt);
            $(".edit_medias_form_copyright").val(media.copyright);
            $(".edit_medias_form_comment").val(media.comment);
        }
    });
    let defaultFile = await getFileFromUrl(window.selectedPath, window.fileName);
    $(uppyContainerEdit).html("");
    if (defaultFile instanceof File) {
        mountNewInstance(uppyContainerEdit);
        addNewFile(defaultFile)
        setNumberFiles();
    }
    hideBlocLoading();

}

async function deleteSelectedMedia(id) {
    showBlocLoading();
    disableSubmitBtn();
    let element = getElement();
    let search = $(element['mediaSearch']).val();
    let result = await deleteMediaById(id, search).catch((e) => {
        enableSubmitBtn();
        hideBlocLoading();
        showErrorAlertMessage(window.mediaDeleteError);
    });
    let data = result.data;
    let pageNumber = parseInt($('.page-number.active').find('.page-link').text() || 1);
    showSuccessAlertMessage(window.mediaDeletedTitle);
    hideAfterClose();
    hideModal(element['closeElement']);
    showImages(data, data.mediaDirectoryId, pageNumber, search);
}


function getSourceIcon(extension, path) {
    let source = '';
    if (extension === 'pdf') {
        source = pdfIconeSource;
    } else if (['mkv', 'avi', 'mov', 'mp4', 'm4v', 'flv', 'wmv', 'webm', 'ogv', 'avif'].includes(extension)) {
        source = videoIconeSource;
    } else if (extension === 'json') {
        source = jsonIconeSource;
    } else if (extension === 'csv') {
        source = csvIconeSource;
    } else if (extension === 'txt') {
        source = txtIconeSource;
    } else if (extension === 'xml') {
        source = xmlIconeSource;
    } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg', 'heif', 'heic', 'raw'].includes(extension)) {
        source = path;
    } else {
        source = documentIconeSource; // Default to document icon for other types
    }

    return source;
}

async function showMediaInfo(id, path, extension) {
    let source = getSourceIcon(extension, path);
    $("#show-modal-image").css("background-image", "url("+ source +"?no="+Math.random()+")");
    await getMediaInfo(id).then((data) => {
        if (data.success) {
            let media = data.success;
            fillInfoMedia(media, 'n/d');
        }
    })
}


function manageBtn(mediaRole) {
    let isChannelDirectory = getSelectedNode().original.isChannelDirectory;
    for (const [_, value] of Object.entries(selectedMediaButtons)) {
        $(value.class).addClass('d-none');
        if (mediaRole == window.role
            || ((isChannelDirectory && mediaRole == window.role) || window.isSuperAdmin)
            || (value.roles.includes(window.role) && !isChannelDirectory)
            || value.excluded) {
           $(value.class).removeClass('d-none');
        }
    }
}

function canShowPreviewBtn(extension) {
    return formatTypes['images'].includes('.' + extension) || formatTypes['pdfs'].includes('.' + extension);
}

async function search(id, search) {
    window.currentElement = 'searchMedia';
    initiateElementWithoutShow();
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
        getImages(id, 1, true, 'getImage', search, "searchMedia");
    }, 300);
    
}

function fillInfoMedia(media, defaultValue = ''){
    $("#show-modal-label-media-extension").text(media?.extension.toUpperCase() ?? defaultValue);
    $("#show-modal-label-media-size").text(bytesToSize(media?.size));
    $("#show-modal-label-media-creation-date").text(media?.createdAt ?? defaultValue);
    $("#show-modal-label-media-folder").text(defaultValue != '' ? getRoute(getSelectedNodeId()):defaultValue);
    $("#show-modal-label-media-title").text(media?.name ?? defaultValue);
    $("#show-modal-label-media-legende").text(media?.textAlt ?? defaultValue);
    $("#show-modal-label-media-copyright").text(media?.copyright ?? defaultValue);
    $("#show-modal-label-media-comment").text(media?.comment ?? defaultValue);
}

export { canShowPreviewBtn, deleteSelectedMedia, fillInfoMedia, getImages, hideBlocLoading, manageBtn, search, showEditMedia, showImages, showMediaInfo, uploadError, uploadSuccess };

