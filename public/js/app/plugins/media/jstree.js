import { showFiles } from '../../manager/media/FolderManager.js';

const tree = () => {
    $('#using_json').jstree({
        "multiple": true,
        'core': {
            'check_callback': true,
            'data': {
                'url': window.treeUrl,
                'data': function (node) {
                    return { 'id': node.id };
                }
            }
        },
        "types": {
            "default": {
                "icon": "far fa-folder"
            }
        },
        "plugins": ["dnd", "types"]
    })
        .on("open_node.jstree", function (e, data) {
            data.instance.set_icon(data.node, "far fa-folder-open");
        })
        .on("close_node.jstree", function (e, data) {
            data.instance.set_icon(data.node, "far fa-folder");
        })
        .on("select_node.jstree", function (e, data) {
            let route = getRoute(data.node.id);
            let isActionsBtn = data.node.original.isActionsBtn;
            showFiles(data.node, route, isActionsBtn);
        });
};

function getRoute(id, glue = ' > ') {
    return getTree().get_path(id, glue);
}

function getTree() {
    return $('#using_json').jstree(true);
}

function createNewNode(parentId, id, input, channelType = '') {
    let addId = getUniqueKey();
    if (channelType == 'GLOBAL') {
        getTree().create_node(parentId,
            {
                "id": id,
                "text": input,
                'isActionsBtn': true
            },
            "first"
        );
    } else {
        getTree().create_node(parentId,
            {
                "id": id,
                "text": input,
                'isActionsBtn': true,
                "children": [
                    {
                        "id": addId,
                        "text": '<i class="fas fa-folder-plus fa-lg pl-1 color-add add-folder"></i>',
                        "icon": false,
                        "parent": id,
                        "add": true,
                        "state": {
                            "disabled": true
                        }
                    }
                ]
            },
            "first"
        );
    }
}

function updateNode(id, input) {
    getTree().set_text(id, input);
}

function removeNode(id) {
    getTree().delete_node(id);
}

function getSelectedNodeId() {
    return parseInt(getTree().get_selected()[0]);
}

function getUniqueKey() {
    let addId = "add" + Math.random().toString(8).slice(2);
    while (getTree().get_node(addId) != false) {
        addId = "add" + Math.random().toString(8).slice(2);
    }
    return addId;
}

function getSelectedNode() {
    return getTree().get_node(getSelectedNodeId());
}


export { createNewNode, getRoute, getSelectedNode, getSelectedNodeId, getTree, removeNode, tree, updateNode };

