function getPagination(id, totalPages, currentPage, defaultFunction = 'getImage', search = '') {
    const toShow = 7; // should be impair
    const skipPages = toShow;
    let liHtml = "";
    $('#previous-media-page').attr('onclick', defaultFunction + '(' + id + ',' + (currentPage - 1) + ', false,'+ defaultFunction + ',"' + search +'")');
    $('#next-media-page').attr('onclick', defaultFunction + '(' + id + ',' + (currentPage + 1) + ', false,'+ defaultFunction + ',"' + search +'")');

    $('#previous-media-10-pages').attr('onclick', defaultFunction + '(' + id + ',' + (currentPage - skipPages) + ', false,'+ defaultFunction + ',"' + search +'")');
    $('#next-media-10-pages').attr('onclick', defaultFunction + '(' + id + ',' + (currentPage + skipPages) + ', false,'+ defaultFunction + ',"' + search +'")');

    // manage step page links
    if (currentPage <= skipPages) {
        $('#previous-media-10-pages').removeClass('disabled').attr('onclick', defaultFunction + '(' + id + ',' + 1 + ', false,'+ defaultFunction + ',"' + search +'")');
    } else {
        $('#previous-media-10-pages').removeClass('disabled');
    }
    if ((totalPages - currentPage) < skipPages) {
        $('#next-media-10-pages').removeClass('disabled').attr('onclick', defaultFunction + '(' + id + ',' + totalPages + ', false,'+ defaultFunction + ',"' + search +'")');
    } else {
        $('#next-media-10-pages').removeClass('disabled');
    }
    // manage disabled previous links
    if (1 == currentPage) {
        $('#previous-media-page').addClass('disabled').removeAttr('onclick');
        $('#previous-media-10-pages').addClass('disabled').removeAttr('onclick');
    } else {
        $('#previous-media-page').removeClass('disabled');
    }
    // manage disabled next links
    if (totalPages == currentPage) {
        $('#next-media-page').addClass('disabled').removeAttr('onclick');
        $('#next-media-10-pages').addClass('disabled').removeAttr('onclick');
    } else {
        $('#next-media-page').removeClass('disabled');
    }

    $('#media-pagination li').removeClass('active');

    $('.page-number').remove();
    let pages = getPaginationPages(totalPages, currentPage, toShow);

    let lastOne = 0;
    pages.forEach((page) => {
        if (page - lastOne > 1) {
            if (page == totalPages) {
                liHtml += addButtonPagination(id, (lastOne + 1), "...", defaultFunction, search)
            } else {
                liHtml += addButtonPagination(id, (page - 1), "...", defaultFunction, search)
            }
        }
        liHtml += addButtonPagination(id, (page), page, defaultFunction, search)
        lastOne = page;
    });

    $(liHtml).insertAfter($('#previous-media-page'));

    $('#page-' + currentPage).addClass('active');
    $('.page-text').show();
    $(".footer-pagination").removeClass('d-none');
}

function addButtonPagination(id, page, show, defaultFunction = 'getImage', search = '') {
    return `<li id="page-${page}" class="page-item page-number" onclick= "${defaultFunction}(${id}, ${page}, false, ${defaultFunction}, '${search}')"><a class="page-link" href="javascript:void(0)">${show}</a></li>`;
}

function getPaginationPages(totalPages, currentPage, toShow) {
    const median = parseInt((toShow - 3) / 2);
    let lastPage = totalPages - 1;
    let firstPage = 2;
    let pages = [];
    if (totalPages <= toShow) {
        pages = Array.from({ length: totalPages }, (_, i) => i + 1);
    } else {
        pages = [1, currentPage, totalPages];
        let lengthLeft = lengthRight = median;
        let left = currentPage - median;
        if (left < firstPage) {
            lengthLeft = (currentPage - firstPage) % median;
            lengthRight = lengthRight + (median - lengthLeft);
        }
        let right = currentPage + median;
        if (right >= totalPages) {
            lengthRight = (lastPage - currentPage) % median;
            lengthLeft = lengthLeft + (median - lengthRight);
        }
        let lefts = Array.from({ length: lengthLeft }, (_, i) => currentPage - i - 1);
        let rights = Array.from({ length: lengthRight }, (_, i) => currentPage + i + 1);
        pages = pages.concat(lefts.concat(rights)).sort((a, b) => a - b);
    }
    pages = [...new Set(pages)];

    return pages;
}

export {
    getPagination
};

