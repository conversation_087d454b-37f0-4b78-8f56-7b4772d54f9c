import { disableSubmitBtn, enableSubmitBtn, getElement } from '../../helpers/MediaHelper.js';
import { hideBlocLoading, showImages, uploadError, uploadSuccess } from '../../manager/media/MediaManager.js';
import extensions from '../../utils/media/MediaExtension.js';
window.uppyMessages = document.getElementById('uppy-messages');
var editMediaName = $("#edit_media_name");
var mediaFileName = $(".media_file_name");

const uppy =
    Uppy.Core({
        debug: false,
        autoProceed: false,
        locale: Uppy.locales[culture],
        restrictions: {
            maxFileSize: Infinity,
            maxNumberOfFiles: 12,
            minNumberOfFiles: 1,
            allowedFileTypes: extensions
        },
        onBeforeFileAdded: (currentFile, files) => {
            enableSubmitBtn();
            editMediaName.val(currentFile?.name.split('.').slice(0, -1).join('.'));
            if (uppy.getFiles().length >= 1) mediaFileName.addClass('d-none'), editMediaName.val('');
            else mediaFileName.removeClass('d-none');
            return currentFile;
        }
    })
        .use(Uppy.Dashboard, {
            inline: true,
            height: 300,
            width: '100%',
            target: '#uppy-container',
            hideUploadButton: true,
            hideRetryButton: true
        })
        .use(Uppy.XHRUpload, {
            endpoint: window.mediaUploadUrl,
            fieldName: 'images[]',
            method: 'post',
            bundle: true,
            getResponseError: function (responseText) {
                return JSON.parse(responseText);
            }
        })
        .on('file-removed', function (f, r) {
            if (uppy.getFiles().length == 0) disableSubmitBtn(), editMediaName.val('');
            else if (uppy.getFiles().length > 1) mediaFileName.addClass('d-none'), editMediaName.val('');
            else mediaFileName.removeClass('d-none'), editMediaName.val(uppy.getFiles()[0]?.name.split('.').slice(0, -1).join('.'));
        })
        .on('error', function (e) {
            enableSubmitBtn();
            hideBlocLoading();
            if (e?.errors) {
                manageUppyGlobalError(e.errors)
            }
            showUppyRemoveBtn();
        }, (e) => {
            if (e?.errors) {
                manageUppyGlobalError(e.errors)
            }
            showUppyRemoveBtn();
        })
        .on('upload-success', function (file, response) {
            const data = response.body
            window.uppyMessages.innerHTML = '';
            $('.uppy-DashboardItem-fileInfoAndButtons').html('');
            if (data.success) {
                let image = data.success;
                let title = window.currentEvent == 'add' ? window.mediaAddTitleAlert : window.mediaEditTitleAlert;
                let pageNumber = parseInt($('.page-number.active').find('.page-link').text() || 1);
                let element = getElement();
                let search = $(element['mediaSearch']).val();
                setTimeout(() => {
                    showImages(data.data, image[0].parentDirectoryId, pageNumber, search);
                }, 2500);
                uploadSuccess(title);
                $('form#' + window.currentEvent + '-media-form').trigger("reset");
                uppy.reset();
            } else {
                var errorMessage = '';
                if (data.error !== 'undefined') {
                    errorMessage = window.mediaValidationError
                } else {
                    if (window.currentEvent == 'add') {
                        errorMessage = window.mediaAddError;
                    } else {
                        errorMessage = window.mediaEditError;
                    }
                }
                uploadError(errorMessage);
            }
        })
        .on('complete', function (result) {
            if (result.failed.length === 0) {
                disableSubmitBtn();
            }
        });


function manageUppyGlobalError(errors) {
    window.uppyMessages.innerHTML = '';
    var existingNames = errors.filter(r => r.status == 409);
    if (existingNames.length > 0) {
        confirm(existingNames);
    }
    errors.forEach(response => {
        let li = document.createElement('li');
        li.appendChild(document.createTextNode(response?.message));
        window.uppyMessages.appendChild(li);
    });
}

function showUppyRemoveBtn() {
    uppy.getFiles().forEach(function (file) {
        // Find the file element in the DOM
        const fileElement = document.getElementById("uppy_" + file.id).querySelector('.uppy-DashboardItem-fileInfoAndButtons');
        if (fileElement) {
            const container = document.createElement('div');
            container.className = 'uppy-DashboardItem-actionWrapper';
            fileElement.appendChild(container);
            const actionsContainer = fileElement.querySelector('.uppy-DashboardItem-actionWrapper');
            // Create and show a remove button
            const removeButton = document.createElement('button');
            removeButton.className = 'uppy-u-reset uppy-c-btn uppy-c-btn--link uppy-DashboardItem-action uppy-DashboardItem-action--remove custom-remove-button';
            removeButton.innerHTML = '<svg aria-hidden="true" focusable="false" class="UppyIcon" width="18" height="18" viewBox="0 0 18 18"><path d="M9 0C4.034 0 0 4.034 0 9s4.034 9 9 9 9-4.034 9-9-4.034-9-9-9z"></path><path fill="#FFF" d="M13 12.222l-.778.778L9 9.778 5.778 13 5 12.222 8.222 9 5 5.778 5.778 5 9 8.222 12.222 5l.778.778L9.778 9z"></path></svg>';
            removeButton.title = 'Remove file';

            // Append the remove button to the actions container
            actionsContainer.appendChild(removeButton);

            // Add click event listener to the remove button
            removeButton.addEventListener('click', () => {
                uppy.removeFile(file.id);
            });

        }
    });
}

function mountNewInstance(elementName) {
    uppy.getPlugin("Dashboard").mount(elementName, '');
    $(elementName + " .uppy-Root").addClass("uppy-size--md mb-4");
}

function addNewFile(file) {
    uppy.cancelAll();
    uppy.addFile({
        name: file.name,
        data: file,
        source: "remote",
        isRemote: false,
    });
}

function setNumberFiles(number = 1) {
    uppy.opts.restrictions.maxNumberOfFiles = number;
}

function confirm(msgs) {
    var msg = '';
    msgs.forEach(m => { msg += '<li class="existing-name">' + m.name + '</li>' });
    var $content = "<div class='dialog-ovelay'>" +
        "<div class='dialog'><header>" +
        " <h3> " + window.mediaExistHeaderAlert + " </h3> " +
        "<i class='fa fa-close'></i>" +
        "</header>" +
        "<div class='dialog-msg'>" +
        "<h6>" + window.mediaExistTitleAlert + "</h6>" +
        " <ul> " + msg + " </ul> " +
        "</div>" +
        "<footer>" +
        "<div class='controls'>" +
        " <button class='button button-danger doAction'>" + window.mediaExistConfirmAlert + "</button> " +
        " <button class='button button-default cancelAction'>" + window.mediaExistCancelAlert + "</button> " +
        "</div>" +
        "</footer>" +
        "</div>" +
        "</div>";
    $('body').prepend($content);
    $('.doAction').click(function () {
        uppy.setMeta({ toErase: true });
        uppy.retryAll();
        $(this).parents('.dialog-ovelay').fadeOut(500, function () {
            $(this).remove();
        });
    });
    $('.cancelAction, .fa-close').click(function () {
        uppy.setMeta({ toErase: false });
        $(this).parents('.dialog-ovelay').fadeOut(500, function () {
            $(this).remove();
        });
    });

}

export { addNewFile, mountNewInstance, setNumberFiles, uppy };

