function toggleClasses(className, show = true) {
    show ? $(className).removeClass('d-none') : $(className).addClass('d-none');
}

function hideClassesAlertAfterTime(className, time) {
    setTimeout(function () {
        toggleClasses(className, false);
    }, time);
}

function hideModal(className) {
    setTimeout(() => {
        $(className).modal('hide');
    }, 2000);
}

function showModal(className) {
    $(className).modal('show');
}

function addAnimateCss(className, animationClass) {
    $(className).removeClass(function (index, className) {
        return (className.match (/(^|\s)animate__\S+/g) || []).join(' ');
    }).addClass(animationClass);
}

export {
    addAnimateCss,
    hideClassesAlertAfterTime,
    hideModal,
    showModal,
    toggleClasses
};

