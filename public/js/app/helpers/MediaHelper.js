import mediaElements from '../utils/media/MediaElements.js';
import { hideClassesAlertAfterTime, hideModal, toggleClasses } from './AnimationHelper.js';


function updateTtile(text) {
    let element = getElement();
    $(element['title']).find('span').html(text);
}

function initiateElement() {
    let element = getElement();
    toggleClasses(element?.hide, false);
    toggleClasses(element?.title);
    toggleClasses(element?.show);
    $(element?.modalName).modal('show');
    $(document).find(element?.removeClass).removeClass(element?.removeClass?.replace('.', ''));
}

function initiateElementWithoutShow() {
    let element = getElement();
    toggleClasses(element?.hide, false);
    toggleClasses(element?.title);
    $(document).find(element?.removeClass).removeClass(element?.removeClass?.replace('.', ''));
}

function manageHeaderListMedia(route, isActionsBtn = false) {
    updateTtile(route);
    checkIfShowTools(isActionsBtn);
}

function showBlocLoading() {
    let element = getElement();
    if(element['contentClass'] && element['hideContentAtLoad']) $(element['contentClass']).addClass('d-none');
    if(element['hide']) $(element['hide']).addClass('d-none');
    toggleClasses(element['load']);
    
}

function hideBlocLoading() {
    let element = getElement();
    toggleClasses(element['load'], false);
    if(element['hide']) $(element['hide']).addClass('d-none');
}

function getContentClass() {
    let element = getElement();
    return element['contentClass'];
}

function getAppendClass() {
    let element = getElement();
    return element['appendContent'];
}

function clearInputs() {
    let element = getElement();
    $(element['clearInputs']).val("");
}

function checkIfShowTools(isActionsBtn) {
    let element = getElement();
    if (isActionsBtn == true) toggleClasses(element['headerAction']);
    else toggleClasses(element['headerAction'], false);
}

function noImages(search = '') {
    let element = getElement();
    if(element['appendContent']) $(element['appendContent']).html('');
    if(search) {
        toggleClasses(element['emptySearch']);
        if(element['contentClass']) $(element['contentClass']).removeClass('d-none');
    } else {
        toggleClasses(element['noFiles']);
        if(element['contentClass']) $(element['contentClass']).addClass('d-none');
    }
    toggleClasses('.footer-pagination', false);
}

function successAction(title) {
    let element = getElement();
    hideBlocLoading();
    showSuccessAlertMessage(title);
    hideModal(element?.['closeElement'])
}

function errorAction(title) {
    hideBlocLoading();
    showErrorAlertMessage(title);
}

function showSuccessAlertMessage(title) {
    let element = getElement();
    $(element['alertSuccessTitle']).html(title);
    toggleClasses(element['alertSuccess']);
    hideClassesAlertAfterTime(element['alertSuccess'],2000);
    setTimeout(function () {
        enableSubmitBtn();
    }, 2000);
}

function showErrorAlertMessage(title) {
    let element = getElement();
    $(element['alertErrorTitle']).html(title);
    toggleClasses(element['alertError']);
    hideClassesAlertAfterTime(element['alertError'],2000);
}

function getElement() {
    let key = window.currentElement;
    return mediaElements[key];
}

function updateRoute(route) {
    updateTtile(route);
}

function disableSubmitBtn() {
    let element = getElement();
    $(element['submitBtn']).prop("disabled", true);
}

function enableSubmitBtn() {
    let element = getElement();
    $(element['submitBtn']).prop("disabled", false);
}

function hideAfterClose() {
    let element = getElement();
    toggleClasses(element['hideAfterCloseElement'], false);
}

function showAfterClose() {
    let element = getElement();
    toggleClasses(element?.['showAfterCloseElement']);
}

function appendClass() {
    let element = getElement();
    $(element?.elementToAppend).addClass(element?.addClass?.replace('.', ''));
}

export {
    appendClass,
    checkIfShowTools,
    clearInputs,
    disableSubmitBtn,
    enableSubmitBtn,
    errorAction,
    getAppendClass,
    getContentClass,
    getElement,
    hideAfterClose,
    hideBlocLoading,
    initiateElement,
    initiateElementWithoutShow,
    manageHeaderListMedia,
    noImages,
    showAfterClose,
    showBlocLoading,
    showErrorAlertMessage,
    showSuccessAlertMessage,
    successAction,
    updateRoute,
    updateTtile
};

