const mediaElements =
{
    'listImages': {
        'show': `
                .list-files__content,
                .operations
            `,
        'hide': `
                .footer-pagination,
                .no-folder__selected__content,
                .no-file__existed_content,
                .operations .edit-media-modal,
                .operations .delete-media-modal,
                .operations .copy-media,
                .empty-search
            `,
        'clearInputs': `
            .media-search
        `,
        'load': '.media-content__loading',
        'contentClass': '.list-files__full__content',
        'appendContent': '.list-files__content',
        'title': '.sub-header',
        'headerAction': `#tools-folder`,
        'removeClass': '.media-content__full',
        'hideContentAtLoad': true,
        'noFiles': '.no-file__existed_content',
        'emptySearch': '.empty-search',
        'totalFiles': '.total-pages',
        'mediaSearch': '.media-search'
    },
    'addFolder': {
        'clearInputs': `
            #bloc_add #add_input
        `,
        'load': '#add-folder-modal #alert-loading',
        'alertSuccess': '#add-folder-modal #alert-success',
        'alertSuccessTitle': '#add-folder-modal #alert-success .title',
        'alertError': '#add-folder-modal .alert-danger',
        'alertErrorTitle': '#add-folder-modal .alert-danger .title',
        'closeElement': 'div#add-folder-modal',
        'modalName': '#add-folder-modal',
        'submitBtn': '.save-add-folder'
    },
    'editFolder': {
        'clearInputs': `
            #bloc_edit #edit_input
        `,
        'load': '#edit-folder-modal #alert-loading',
        'alertSuccess': '#edit-folder-modal #alert-success',
        'alertSuccessTitle': '#edit-folder-modal #alert-success .title',
        'alertError': '#edit-folder-modal .alert-danger',
        'alertErrorTitle': '#edit-folder-modal .alert-danger .title',
        'closeElement': 'div#edit-folder-modal',
        'modalName': '#edit-folder-modal',
        'title': '.sub-header',
        'submitBtn': '.save-edit-folder',
    },
    'deleteFolder': {
        'load': '#delete-folder-modal #alert-loading',
        'alertSuccess': '#delete-folder-modal #alert-success',
        'alertSuccessTitle': '#delete-folder-modal #alert-success .title',
        'alertError': '#delete-folder-modal .alert-danger',
        'alertErrorTitle': '#delete-folder-modal .alert-danger .title',
        'closeElement': 'div#delete-folder-modal',
        'modalName': '#delete-folder-modal',
        'hideAfterCloseElement': `
            .media-content__loading,
            .no-file__existed_content,
            .list-files__full__content,
            .sub-header
        `,
        'showAfterCloseElement': `
            .no-folder__selected__content
        `,
        'addClass': 'media-content__full',
        'elementToAppend': '.media-content',
        'submitBtn': '.save-delete-folder',
    },
    'addMedia': {
        'hideAfterCloseElement': `
                .operations .edit-media-modal,
                .operations .delete-media-modal,
                .operations .copy-media,
                .no-folder__selected__content,
                .no-file__existed_content
            `,
        'load': '#add-media-modal #alert-loading',
        'alertSuccess': '#add-media-modal #alert-success',
        'alertSuccessTitle': '#add-media-modal #alert-success .title',
        'alertError': '#add-media-modal .alert-danger',
        'alertErrorTitle': '#add-media-modal .alert-danger .title',
        'submitBtn': '#media-form-add-button',
        'cancelBtn': '',
        'closeElement': 'div#add-media-modal',
        'contentClass': '.list-files__full__content',
        'hideContentAtLoad': false,
        'appendContent': '.list-files__content',
        'mediaSearch': '.media-search',
        'emptySearch': '.empty-search',
        'totalFiles': '.total-pages',
    },
    'editMedia': {
        'hideAfterCloseElement': `
            .operations .edit-media-modal,
            .operations .delete-media-modal,
            .operations .copy-media,
            .no-folder__selected__content,
            .no-file__existed_content
            `,
        'load': '#edit-media-modal #alert-loading',
        'alertSuccess': '#edit-media-modal #alert-success',
        'alertSuccessTitle': '#edit-media-modal #alert-success .title',
        'alertError': '#edit-media-modal .alert-danger',
        'alertErrorTitle': '#edit-media-modal .alert-danger .title',
        'submitBtn': '#media-form-edit-button',
        'cancelBtn': '',
        'closeElement': 'div#edit-media-modal',
        'contentClass': '.list-files__content',
        'hideContentAtLoad': false,
        'appendContent': '.list-files__content',
        'mediaSearch': '.media-search',
        'noFiles': '.no-file__existed_content',
        'emptySearch': '.empty-search',
        'totalFiles': '.total-pages',
    },
    'deleteMedia': {
        'hideAfterCloseElement': `
                .operations .edit-media-modal,
                .operations .delete-media-modal,
                .operations .copy-media
            `,
        'load': '#delete-media-modal #alert-loading',
        'alertSuccess': '#delete-media-modal #alert-success',
        'alertSuccessTitle': '#delete-media-modal #alert-success .title',
        'alertError': '#delete-media-modal .alert-danger',
        'alertErrorTitle': '#delete-media-modal .alert-danger .title',
        'submitBtn': '.confirm-delete-media',
        'cancelBtn': '',
        'closeElement': 'div#delete-media-modal',
        'contentClass': '.list-files__full__content',
        'hideContentAtLoad': false,
        'appendContent': '.list-files__content',
        'noFiles': '.no-file__existed_content',
        'emptySearch': '.empty-search',
        'mediaSearch': '.media-search',
        'totalFiles': '.total-pages',
    },
    'searchMedia': {
        'show': `
                .list-files__content,
                .operations
            `,
        'hide': `
            .footer-pagination,
            .no-folder__selected__content,
            .no-file__existed_content,
            .operations .edit-media-modal,
            .operations .delete-media-modal,
            .operations .copy-media,
            .empty-search
            `,
        'load': '.media-content__loading',
        'contentClass': '.list-files__content',
        'appendContent': '.list-files__content',
        'title': '.sub-header',
        'headerAction': `#tools-folder`,
        'removeClass': '.media-content__full',
        'hideContentAtLoad': true,
        'emptySearch': '.empty-search',
        'totalFiles': '.total-pages',
        'mediaSearch': '.media-search'
    }
}


export default mediaElements;