import { addAnimateCss } from '../../helpers/AnimationHelper.js';
import { disableSubmitBtn, getElement, showBlocLoading } from '../../helpers/MediaHelper.js';
import { canShowPreviewBtn, deleteSelectedMedia, fillInfoMedia, manageBtn, search, showEditMedia, showMediaInfo } from '../../manager/media/MediaManager.js';
import { getSelectedNodeId } from '../../plugins/media/jstree.js';
import { mountNewInstance, uppy } from '../../plugins/media/uppy.js';
import { selectedMediaButtons } from '../../utils/media/MediaActions.js';
import { buttons } from '../../utils/media/MediaButtons.js';
import { formatTypes } from '../../utils/media/MediaFormatType.js';

const uppyInstance = uppy;
var $addMediaModal = "#add-media-modal";
var $editMediaModal = "#edit-media-modal";
var $deleteMediaModal = "#delete-media-modal";
var $showMediaModal = "#show-media-modal";
var $previewMediaModal = "#preview-media-modal";
var $previewMediaImage = "#preview-modal-image";
var $showModalPreviewBtn = ".show-modal-preview-btn";
var $closeModalPreviewBtn = "#preview-modal-close-btn";
var $showModalDownloadBtn = ".show-modal-download-btn";
const toast = document.querySelector(".toast");
let closeIcon = ".close",
    progress = document.querySelector(".progress"),
 timer1, timer2;


$(buttons['saveAddMedia'].class).on('click', () => {
    showBlocLoading();
    window.currentEvent = 'add';
    let element = getElement();
    let search = $(element['mediaSearch']).val();
    window.uppyMessages = document.getElementById('uppy-messages');
    let id = getSelectedNodeId();
    uppy.setMeta({ parent_id: id });
    uppyInstance.removePlugin(uppyInstance.getPlugin('XHRUpload'));
    uppyInstance.use(Uppy.XHRUpload, {
        endpoint: window.mediaUploadUrl.replace(':id', null),
        fieldName: 'images[]',
        method: 'post',
        bundle: true,
        getResponseError: function (responseText) {
            return JSON.parse(responseText);
        }
    });
    uppyInstance.setMeta({ 'data': $("form#add-media-form").serialize(), 'search': search })
    uppyInstance.upload().then(r => r).catch(e => e);
    uppyInstance.retryAll();
});

$(buttons['saveEditMedia'].class).on('click', () => {
    showBlocLoading();
    window.currentEvent = 'edit';
    let element = getElement();
    let search = $(element['mediaSearch']).val();
    window.uppyMessages = document.getElementById('uppy-messages-edit');
    let id = getSelectedNodeId();
    uppy.setMeta({ parent_id: id });
    uppyInstance.removePlugin(uppyInstance.getPlugin('XHRUpload'));
    uppyInstance.use(Uppy.XHRUpload, {
        endpoint: window.mediaUploadUrl.replace(':id', window.selectedId),
        fieldName: 'images[]',
        method: 'post',
        bundle: true,
        getResponseError: function (responseText) {
            return JSON.parse(responseText);
        }
    });
    uppyInstance.setMeta({ 'data': $("form#edit-media-form").serialize(), 'media': window.selectedId, 'search': search})
    uppyInstance.upload().then(r => r).catch(e => e);
    uppyInstance.retryAll();
})

window.getFileFromUrl = async (url, name) => {
    const response = await fetch(url, { 
        headers: {
                'Cache-Control': 'no-cache'
         }
     }).catch(e => new Error('Canceled'));
    if (response.headers) {
        const contentType = response.headers.get("Content-Type") || "application/octet-stream";
        const data = await response.blob();
        return new File([data], name, {
            type: contentType,
        });
    }
}

$('body').on('click', '.selected-image', (e) => {
    e.stopPropagation();
    let id = $(e.currentTarget).attr('data-id'),
        mediaRole = $(e.currentTarget).attr('data-role');
    let path = $(e.currentTarget).find('img').attr('data-path');
    window.selectedId = id;
    window.selectedPath = path;
    window.fileName = $(e.currentTarget).attr('data-filename');
    $('.selected').removeClass('selected');
    $(e.currentTarget).addClass("selected");
    $(selectedMediaButtons['copy'].class).attr('data-url', path);
    manageBtn(mediaRole);
})

$(buttons['confirmDeleteMedia'].class).on('click', (e) => {
    e.preventDefault();
    deleteSelectedMedia(window.selectedId);
});

$("body").on('click', buttons['copyButtonMedia'].class,() => {
    var copyMediaLink = document.getElementById('copy-media');
    var url = copyMediaLink.getAttribute('data-url');
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(function () {
            showToast();
        }).catch(function () {
        });
    } else {
        var textarea = document.createElement('textarea');
        textarea.value = url;
        document.body.appendChild(textarea);
        textarea.select();
        try {
            document.execCommand('copy');
            showToast();
        } catch (err) {
        }
        document.body.removeChild(textarea);
    }
});

$(buttons['typeFormatDetail'].class).on('click', (e) => {
    let type = $(e.currentTarget).attr('data-type');
    var content = (formatTypes[type]).join(', ') || '<p>No formats available</p>';
    document.getElementById('format-card-content').innerHTML = content;
    var formatCardModal = new bootstrap.Modal(document.getElementById('format-card-modal'));
    formatCardModal.show();
})

$('body').on("click", closeIcon, () => {
    toast.classList.remove("active");
    setTimeout(() => {
        progress.classList.remove("active");
    }, 300);
    clearTimeout(timer1);
    clearTimeout(timer2);
}
);

$($editMediaModal).on("hidden.bs.modal", function () {
    $("#uppy-container").html("");
    $('#uppy-messages-edit').html('');
    uppy.setMeta({ toErase: false });
    mountNewInstance("#uppy-container");
});

$($addMediaModal).on("hidden.bs.modal", function () {
    $('#uppy-messages').html('');
    uppy.setMeta({ toErase: false });
});

$($showMediaModal).on("hidden.bs.modal", function () {
    fillInfoMedia()
});

$('a[href="' + $addMediaModal + '"]').on('click', function (event) {
    window.currentElement = "addMedia";
    uppyInstance.opts.restrictions.maxNumberOfFiles = 12;
    event.preventDefault();
    uppyInstance.cancelAll();
    disableSubmitBtn();
    $($addMediaModal).modal('show');
});

$('a[href="' + $editMediaModal + '"]').on('click', async function (event) {
    event.preventDefault();
    window.currentElement = "editMedia";
    showEditMedia(window.selectedId);
});

$('body').on('click', 'a[href="' + $deleteMediaModal + '"]', function (event) {
    event.preventDefault();
    window.currentElement = "deleteMedia";
    $($deleteMediaModal).modal('show');
})

$('body').on('click', 'a[href="' + $showMediaModal + '"]', async function (event) {
    event.stopPropagation();
    let id = $(this).parent().attr("data-id");
    let url = $(this).parent().attr("data-path");
    let extension = $(this).parent().attr("data-extension");
    window.previewModalMediaPath = url+"?no="+Math.random();
    window.nameFileMedia = url.split('/').pop();
    $($showModalPreviewBtn).attr('href', url+"?no="+Math.random());
    if(canShowPreviewBtn(extension.toLowerCase())) {
        window.extension = extension;
        $($showModalPreviewBtn).removeClass('d-none');
        $($showModalDownloadBtn).removeClass('d-none');
    } else {
        $($showModalPreviewBtn).addClass('d-none');
        $($showModalDownloadBtn).removeClass('d-none');
    }
    showMediaInfo(id, url, extension);
    $($showMediaModal).modal('show');
});

$(document).on('click', $showModalDownloadBtn, async function(e){
    e.preventDefault();
    let url = window.previewModalMediaPath;
    let tmpUrl=  await getFetchedUrl(url);
    let element = document.createElement('a');
    element.setAttribute('href', tmpUrl);
    element.setAttribute('download', window.nameFileMedia);
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
});

$('body').on('click', $showModalPreviewBtn, function(event) {
    event.preventDefault();
    if(formatTypes['pdfs'].includes('.' + window.extension)){
       window.open(window.previewModalMediaPath, '_blank');
       return;
    }
    $($showMediaModal + ' .modal-content').addClass('d-none');
    addAnimateCss($previewMediaModal + ' ' + $previewMediaImage, 'animate__fadeInUp');
    $($previewMediaModal).removeClass('d-none').fadeIn();
    $($previewMediaImage + '>img').attr('src', window.previewModalMediaPath);
});

$('body').on('click', $closeModalPreviewBtn, function(event) {
    event.preventDefault();
    $($showMediaModal + ' .modal-content').removeClass('d-none');
    addAnimateCss($previewMediaModal + ' ' + $previewMediaImage, 'animate__fadeOutDown')
    $($previewMediaModal).fadeOut();
});

$('.media-search').on('keyup', function() {
    let id = getSelectedNodeId();
    search(id, $(this).val());
});

function showToast() {
    toast.classList.add("active");
    toast.style.display = 'block';
    progress.classList.add("active");

    timer1 = setTimeout(() => {
        toast.classList.remove("active");
    }, 3000);

    timer2 = setTimeout(() => {
        progress.classList.remove("active");
    }, 3300);
}

async function getFetchedUrl(url){

    const response = await fetch(url).catch(e => new Error('Canceled'));
        const data = await response.blob();
        return window.URL.createObjectURL(data);
}