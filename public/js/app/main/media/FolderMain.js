import {
    addNew<PERSON>old<PERSON>,
    remove<PERSON><PERSON><PERSON>,
    showAdd<PERSON>older,
    showD<PERSON><PERSON><PERSON>older,
    showEditFolder,
    updateFolder
} from '../../manager/media/FolderManager.js';
import { getImages } from '../../manager/media/MediaManager.js';
import { getTree, tree } from '../../plugins/media/jstree.js';
import { buttons } from '../../utils/media/FolderButtons.js';

tree();

window.getImage = getImages;

$('body').on('click', '.add-folder', function(){
    let id = $(this).parent().parent().attr('id');
    let node = getTree().get_node(id);
    window.idAddFolder = node.original.parent;
    showAddFolder();
});

$(buttons['saveAddFolder'].class).on('click', () => {
    let input = $("#add_input")[0].value;
    let id = window.idAddFolder;
    addNewFolder(id, input);
});

$(buttons['showEditFolder'].class).on('click', () => {
    let selectedNode = getTree().get_node(getTree().get_selected()[0]);
    showEditFolder(selectedNode.text);
});

$(buttons['saveEditFolder'].class).on('click', async () => {
    let selectedNode = getTree().get_node(getTree().get_selected()[0]);
    let input = $("#bloc_edit #edit_input")[0].value;
    let id = selectedNode.id;
    await updateFolder(id, input);
});

$(buttons['showDeleteFolder'].class).on('click', () => {
    showDeleteFolder();
});

$(buttons['saveDeleteFolder'].class).on('click', () => {
    let selectedNode = getTree().get_node(getTree().get_selected()[0]);
    let id = selectedNode.id;
    removeFolder(id);
});