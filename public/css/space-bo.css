/* Arrange elements */

.multiselect-btn-center {
  width: 60%;
  margin: auto;
}

.bottom-0 {
  bottom: 0 !important;
}

.is-invalid~ul {
  list-style-type: none;
  padding-inline-start: 0;
}

.is-invalid~ul li,
.is-invalid ul li {
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #dc3545;
}

.form-radio div input {
  margin-right: 0.3em;
}

.form-radio div label {
  margin-right: 1em;
}

.form-group.required .control-label:after {
  content: "*";
  color: red;
}

.feature-label {
  font-weight: 400 !important;
}

.feature-label:first-letter {
  text-transform: uppercase;
}

.header-dropdown {
  padding: 6px 18% 6px 12px;
  border-radius: 2px;
  border: solid 0.5px #e3e3e3;
  width: 100%;
  background: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="24" height="24" viewBox="0 0 24 24"%3E%3Cpath fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m6 9l6 6l6-6"%2F%3E%3C%2Fsvg%3E') right center / 20% 1.5em no-repeat;
  background-color: #fff;
}

.header-language {
  display: none;
}

.user-login {
  position: absolute;
  bottom: 18px;
  right: 0;
  height: 40px;
  display: flex;
  align-items: center;
  margin: 6px;
}

.user-login__username {
  display: inline-block;
  margin: 0 4px;
  padding: 0;
  font-family: Roboto, Helvetica, sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 1;
  text-align: right;
  color: var(--primary_color_1);
  cursor: pointer;
}

:not(.OP-style)>.sidemenu .user-login__username {
  color: white;
}

:not(.OP-style)>.sidemenu .user-login__circle {
  background-color: white;
}

:not(.OP-style)>.sidemenu .user-login__circle span {
  color: var(--primary_color_1);
}

.nav-icon {
  padding-right: 5px;
}

.user-login__circle {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  padding: 9px;
  background-color: var(--primary_color_1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-login__circle span {
  font-family: Roboto, Helvetica, sans-serif;
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
  text-align: center;
  color: var(--primary_bg_color_1);
}

.user-login__toggle {
  display: flex;
  align-items: center;
}

.user-login__menu {
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.user-login__menu a {
  color: #333;
  text-decoration: none;
  padding: 5px 10px;
}

.user-login__menu a:hover {
  background-color: #f5f5f5;
}

.user-login__menu .dropdown-divider {
  margin: 5px 0;
}

.user-login:hover .user-login__menu,
.user-login__username:focus+.user-login__menu {
  display: block;
}

.user-login--no-dropdown .user-login__menu {
  display: none;
}

.navbar__block {
  background-color: #f4f6f9;
  border-bottom: none;
  padding-left: 16px;
  padding-right: 16px;
}

.dropdown-menu {
  right: 0;
  left: auto;
  text-align: left;
}

.fontAwesome {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.sidebar {
  height: 100vh;
  position: sticky;
  top: 0;
  border-bottom: 0cm;
  padding-right: 0px !important;
  width: 24rem !important;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px !important;
  overflow: hidden;
}

.logo__image {
  margin-top: 0;
  width: 70px;
  max-height: 90px;
  object-fit: contain;
}

.logo__text {
  margin-left: 0;
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: .05rem;
}

.sidebar .user-login {
  position: static;
  margin: 10px;
  justify-content: center;
  padding: 18px 0px 0px 0px;
}

.sidebar hr {
  margin-left: 20px;
  margin-right: 20px;
}

.main-link {
  padding: 0px;
  margin-top: 10px;
  font-weight: bold;
}

.content {
  background-color: #f1f0f0;
  flex: 1;
}

.btn-apply {
  padding: 0px 10px;
}

.btn-apply button {
  background-color: #243782;
  border-radius: 2px;
  width: 100%;
}

.wrapper .row {
  margin-right: 0;
}

.wrapper .row .content {
  padding-right: 0;
}

.header-section {
  height: 80px;
  min-height: 80px;
  padding: 12px;
  margin-top: 1px;
}

.header-section-border {
  border-bottom: 2px solid white;
  box-shadow: 0px 0px 8px;
}

.header-user {
  text-align: right;
  position: relative;
  padding: 36px;
}

.icon-link {
  margin: 0px 1px;
}

.body-content {
  padding: 18px 0px 0 18px;
}

.header-profile {
  height: 45px;
}

.login-page {
  align-items: center;
  background-color: #243782;
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: center;
}

.login-box {
  width: 360px;
  margin-top: .5rem;
}

.page-content {
  color: black;
  margin-left: 20%;
  margin-top: 20%;
}

.page-content span {
  font-size: 3em;
  font-weight: bold;
  text-transform: uppercase;
}

.page-content p {
  font-size: 1em;
  margin-top: 1em;
}

/* autocomplete */
.main-sidebar {
  z-index: 1;
}

/*autocomplete search*/
.ui-autocomplete {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0 0;
  list-style: none;
  background-color: #fff;
  border-color: rgba(0, 0, 0, 0.2);
  border: 1px solid #ccc;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.ui-autocomplete li {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  font-style: black;
  white-space: nowrap;
}

.ui-autocomplete li:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

.ui-menu .ui-state-active {
  margin: 0px !important;
}

.favorite-icon {
  margin: 4px;
  transform: rotate(0deg) !important;
  cursor: default;
}

ul#favorites-list {
  position: relative;
}

#favorites-list .sub-link {
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%;
  white-space: nowrap;
}

.ui-widget-content a {
  text-decoration-line: none;
  font-size: .9em;
}

/* positions */
.right {
  position: absolute;
  right: 0;
}

/* Loader */
.space-loader-ring {
  display: inline-block;
  position: absolute;
  width: 100%;
  height: -webkit-fill-available;
  opacity: 0.5;
  background-color: black;
  z-index: 2;
}

.space-loader-container {
  position: absolute;
  width: 100%;
  height: -webkit-fill-available;
  background-color: black;
  opacity: 0.3;
  z-index: 10;
}

.space-loader-ellipsis {
  position: absolute;
  width: 80px;
  height: 40px;
  z-index: 15;
  margin: auto;
  right: 0;
  left: 0;
  top: 0;
  bottom: 0;
}

.space-loader-ellipsis div {
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
  position: absolute;
  top: 50%;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: #fff;
}

.space-loader-ellipsis div:nth-child(1) {
  left: 8px;
  animation: space-loader-ellipsis1 0.6s infinite;
}

.space-loader-ellipsis div:nth-child(2) {
  left: 8px;
  animation: space-loader-ellipsis2 0.6s infinite;
}

.space-loader-ellipsis div:nth-child(3) {
  left: 32px;
  animation: space-loader-ellipsis2 0.6s infinite;
}

.space-loader-ellipsis div:nth-child(4) {
  left: 56px;
  animation: space-loader-ellipsis3 0.6s infinite;
}

@keyframes space-loader-ellipsis1 {
  0% {
    transform: scale(0);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes space-loader-ellipsis3 {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(0);
  }
}

@keyframes space-loader-ellipsis2 {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(24px, 0);
  }
}

.login-card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 2rem;
  background-color: #fff;
  border-top: 0;
}

.Titre {
  margin: 0 7px 20px;
  font-family: EncodeSans;
  font-size: 34px;
  font-weight: bold;
  font-stretch: semi-expanded;
  line-height: 1.35;
  letter-spacing: normal;
}

.default-login {
  /* font-family: PSAGroupeHMISans; */
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: normal;
  text-align: left;
  margin: 20px 0px;
}

.login-card-body .form-group {
  margin: 10px 0px;
}

.btn-access {
  margin-top: 20px;
}

.btn-access button {
  border-radius: 2px;
  background-color: #4766ff;
}

.form-group label {
  margin: 3px 0px;
  font-weight: bold;
}

.form-group select.form-control {
  background: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="24" height="24" viewBox="0 0 24 24"%3E%3Cpath fill="none" stroke="%234766ff" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m4 9l8 8l8-8"%2F%3E%3C%2Fsvg%3E') no-repeat center / contain;
  background-size: 20px;
  background-position: calc(100% - 10px);
}

#search-bar.form-control {
  border-radius: 0 6px 6px 0;
}

@font-face {
  font-family: "EncodeSans";
  font-style: normal;
  font-weight: 400;
  src: url(../plugins/fontawesome-free/webfonts/EncodeSansSemiExpanded-Bold.woff2) format("woff2"),
    url(../plugins/fontawesome-free/webfonts/EncodeSansSemiExpanded-Bold.woff) format("woff"),
    url(../plugins/fontawesome-free/webfonts/EncodeSansSemiExpanded-Bold.ttf) format("truetype");
}

body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
}

.userSettings__card {
  margin-top: -60px;
}


.no-gutters {
  margin-right: 0px;
  margin-left: 0px;
}

.form-group {
  margin-bottom: 30px;
}

.float-right {
  float: right !important;
}

.float-left {
  float: left !important;
}

.user-prefrences-card {
  padding: 0 1rem;
}

.user-prefrences-card .form-group {
  margin: 10px 0px;
}

.vertical-nav-link {
  border-color: transparent #dee2e6 transparent transparent;
}

.nav-tabs.flex-column .nav-link.active {
  border-color: #dee2e6 transparent #dee2e6 #dee2e6;
}

/*SPMDWBK-304*/
.dataTables_wrapper .dataTables_length {
  margin-bottom: 3%;
}

.jstree-node {
  margin-bottom: 1% !important;
}

.jstree-node select {
  margin-right: 1%;
}

div.dataTables_wrapper div.dataTables_length select {
  width: 75px;
  display: inline-block;
  padding-bottom: 4%;
}

div.dataTables_wrapper div.dataTables_filter input {
  margin-left: 0.5em;
  display: inline-block;
  width: auto;
}

.input-sm {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
  padding-bottom: 4%;
}

table.dataTable thead th,
table.dataTable thead td,
table.dataTable tfoot th,
table.dataTable tfoot td {
  vertical-align: middle;
}

table.dataTable th,
table.dataTable td {
  vertical-align: middle;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1;
}

.non-editable {
  background-color: #e9ecef !important;
  opacity: 1;
  pointer-events: none;
}

/* SPMDWBK-305 */

.pagination {
  --bs-pagination-border-width: none !important;
  border-radius: 3px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  padding: 0px !important;
}

#dt_translation_key_list_previous,
#dt_translation_key_list_next {
  margin-top: 2%;
}

.disabled>.page-link {
  background-color: transparent !important;
}

.dataTables_wrapper .dataTables_paginate {
  padding-top: 1.25em !important;
}

input[disabled="disabled"] {
  background-color: var(--bs-gray-200) !important;
}

.left-tabs.nav-tabs {
  border-right: 1px solid #dee2e6;
  border-bottom: none;
}

.vertical-nav-link.nav-link.active {
  border-top: 1px solid #dee2e6;
  border-right: 1px solid transparent !important;
  border-bottom: 1px solid #dee2e6;
  border-left: 1px solid #dee2e6;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  background-color: #dee2e6;
  font-weight: 600;
}

.paginate_button.previous,
.paginate_button.next {
  margin-top: 2% !important;
}

.widget-info dl {
  list-style-type: none;
}

/********** Menu Style ***********/
.main {
  margin-left: 24rem;
  width: -moz-available;
  width: -webkit-fill-available;
  width: fill-available;
}

.sidenav {
  position: fixed;
  top: 0;
  left: 0;
}

.navbar-navv {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, rgb(59, 58, 58) 0%, hsl(0, 0%, 47%) 100%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-bottom: 0;
  list-style: none;
}

hr.sidebar-divider {
  border-top: 1px solid white;
}

#search-bar::placeholder {
  color: white;
}

.fa-search {
  position: absolute;
  top: 28px;
  right: 37px;
  color: #fff;
}

.search {
  position: relative;
  display: grid;
  margin: 0px 10px;
}

#search-bar {
  background: #484848b0;
  width: auto;
  padding: 10px 15px;
  border-radius: 25px;
  outline: none;
  margin: 15px 12px;
  color: #ffffff;
  box-shadow: 0 .10rem 1.70rem 0 rgba(58, 59, 69, .15) !important;
  border: none;
  font-weight: 700;
  width: -webkit-fill-available;
}

.scrollable {
  overflow-y: auto;
  overflow-x: hidden;
  /* height: 84vh!important; */
}

.menu-icon{
  padding-right: 5px;
}

.bloc-menu {
  background: #f7f7f7f7;
  border-radius: 10px;
  margin: 30px 1rem 0;
  padding: 2px 5px;
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  width: -webkit-fill-available;
}

.bloc-menu .bloc-link:not(:last-child) {
  border-bottom: 1px solid #ffffff8a;
}

.bloc-menu .link-menu {
  color: #1d1d1dc7;
  cursor: pointer;
  display: inline-flex;
  justify-content: space-between;
  width: 100%;
  padding: 11px 15px;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  text-decoration: none !important;
}

.bloc-menu .link-menu .chevron-icon {
  margin-top: 3px;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #3b3b3b33;
  border-radius: 50px;
  box-shadow: 0 0.15rem 1.75rem 0 #b0b0b063;
}

.bloc-link .active-link {
  text-underline-offset: 8px;
  border-radius: 50px;
  font-weight: 600;
  box-shadow: 0 0.15rem 1.75rem 0 #afc2e0ed !important;
}

.disabled-link {
  pointer-events: none;
  color: grey !important;
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-unstyled a {
  word-break: break-all;
  text-underline-offset: 6px;
}

.list-unstyled a:hover {
  transition: transform 0.25s ease-out;
}

.sub-menu-link {
  padding: 2px 15px;
}

.sub-menu-link .sub-link {
  padding: 8px 13px !important;
  font-size: 14px !important;
}

.sub_menu:has(.active-link) {
  display: block;
}


/****** AC Style ******/
.AC-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, hsla(4, 77%, 48%, 1) 0%, hsl(4deg 65.99% 54.25%) 100%);
}

.AC-style #search-bar {
  background: #78201a7a;
}

.AC-style .bloc-menu .link-menu {
  color: #9d1006db;
}

.AC-style .bloc-link .active-link {
  background: #d92a1e !important;
  color: white !important;
  padding: 11px 13px !important;
}

.AC-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background-color: #ffc3bfc9;
  box-shadow: 0 0.15rem 1.75rem 0 #ffa19a5c !important;
}

/****** AP Style ******/
.AP-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, #0072ff 80%, #00d4ff 120%);
}

.AP-style #search-bar {
  background: #89ade780;
}

.AP-style .bloc-menu,
.VX-style .bloc-menu,
.SP-style .bloc-menu,
.CY-style .bloc-menu,
.DG-style .bloc-menu,
.RM-style .bloc-menu,
.MA-style .bloc-menu,
.XX-style .bloc-menu {
  background: #e0ecff;
}

.AP-style .bloc-menu .link-menu {
  color: #002b61b8;
}

.AP-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #c4d6f4;
  box-shadow: 0 0.15rem 1.75rem 0 #c1d8ffbf;
}

.AP-style .active-link {
  background: #006FDE !important;
  color: white !important;
}

/****** DS Style ******/
.DS-style,
.LA-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, hsla(180, 2%, 10%, 1) 0%, hsla(0, 0%, 17%, 1) 100%);
}

.DS-style #search-bar,
.LA-style #search-bar {
  background: #484848b0;
}

.DS-style .bloc-menu,
.LA-style .bloc-menu,
.OP-style .bloc-menu {
  background: #f9f9f9f7;
}

.DS-style .bloc-menu .bloc-link,
.LA-style .bloc-menu .bloc-link,
.OP-style .bloc-menu .bloc-link {
  border-bottom: 1px solid #dadada8a;
}

.DS-style .link-menu,
.LA-style .link-menu {
  color: #1d1d1dc7;
}

.DS-style .bloc-link .active-link,
.LA-style .bloc-link .active-link {
  background: #1e1f1f !important;
  color: white !important;
}

.DS-style .bloc-menu .link-menu.sub-link:not(.active-link):hover,
.LA-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #3b3b3b33;
  box-shadow: 0 0.15rem 1.75rem 0 #b0b0b063;
}

/****** OP Style ******/
.OP-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, #F7FF14 0%, #F7FF14 100%);
}

.OP-style #search-bar {
  background: #0c0c0ca3;
}

.OP-style .logo__text {
  color: #0c0c0ca3;
}

.OP-style hr.sidebar-divider {
  border-top: 3px solid rgba(95, 95, 95, 0.3);
}

.OP-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #3b3b3b33;
  box-shadow: 0 0.15rem 1.75rem 0 #b0b0b063;
}

.OP-style .bloc-link .active-link {
  background: #1e1f1f !important;
  color: white !important;
}

/**** VX style ********/
.VX-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, hsla(230, 100%, 64%, 1) 0%, hsla(230, 100%, 64%, 1) 100%);
}

.VX-style .bloc-menu .link-menu {
  color: #3247b3f0;
}

.VX-style #search-bar {
  background: #89ade780;
}

.VX-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #c4d6f4;
  box-shadow: 0 0.15rem 1.75rem 0 #c1d8ffbf;
}

.VX-style .bloc-link .active-link {
  background: #3247b3f0 !important;
  color: white !important;
}

/**** SP style ********/
.SP-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, rgb(71, 102, 255), rgb(50, 71, 179));
}

.SP-style #search-bar {
  background: #5a73ea;
}

.SP-style .bloc-menu .link-menu {
  color: #3247b3;
}

.SP-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #c4d6f4;
  border-radius: 50px;
  box-shadow: 0 0.15rem 1.75rem 0 #c1d8ffbf;
}

.SP-style .bloc-link .active-link {
  background: #3247b3 !important;
  color: white !important;
}

/**** FT style ********/
.FT-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, rgb(44, 129, 140), rgb(74, 167, 105));
}

.FT-style #search-bar {
  background: #4AA769;
}

.FT-style .link-menu {
  color: #4AA769;
}

.FT-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #c2f8d4;
  border-radius: 50px;
  box-shadow: 0 0.15rem 1.75rem 0 #c1d8ffbf;
}

.FT-style .bloc-link .active-link {
  background: #4AA769 !important;
  color: white !important;
}

/**** FO style ********/
.FO-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, rgb(154, 23, 46), rgb(180, 23, 0));
}

.FO-style #search-bar {
  background: #5d19147a;
}

.FO-style .bloc-menu .link-menu,
.AH-style .bloc-menu .link-menu {
  color: #9d1006db;
}

.FO-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #f4bfbadb;
  border-radius: 50px;
  box-shadow: 0 0.15rem 1.75rem 0 #ffa19a5c !important;
}

.FO-style .bloc-link .active-link,
.AH-style .bloc-link .active-link {
  background: #9d1006db !important;
  color: white !important;
}

/**** AH style ********/
.AH-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, rgb(231, 32, 32), rgb(235, 31, 0));
}

.AH-style #search-bar {
  background: #b71717;
}

.AH-style .bloc-menu .link-menu.sub-link:not(.active-link):hover,
.AR-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #f4bfbad9;
  border-radius: 50px;
  box-shadow: 0 0.15rem 1.75rem 0 #ffa19a5c !important;
}

/**** AR style ********/
.AR-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, rgb(186, 8, 22), rgb(251, 42, 54));
}

.AR-style #search-bar {
  background: #fb2a3489;
}

.AR-style .bloc-menu .link-menu {
  color: #ba0817;
}

.AR-style .bloc-link .active-link {
  background: #ba0817 !important;
  color: white !important;
}

/**** CY style ********/
.CY-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, rgb(71, 102, 255), rgb(50, 71, 179));
}

.CY-style #search-bar {
  background: #89ade780;
}

.CY-style .bloc-menu .link-menu {
  color: #3247b3;
}

.CY-style .bloc-menu .link-menu.sub-link:not(.active-link):hover,
.DG-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #bacdfd;
  box-shadow: 0 0.15rem 1.75rem 0 #c1d8ffbf;
}

.CY-style .bloc-link .active-link {
  background: #3247b3 !important;
  color: white !important;
}

/**** DG style ********/
.DG-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, rgb(71, 102, 255) 0%, rgb(71, 102, 255) 100%);
}

.DG-style #search-bar {
  background: rgb(55, 78, 196);
}

.DG-style .bloc-menu .link-menu {
  color: #3247b3;
}

.DG-style .bloc-link .active-link {
  background: #3247b3 !important;
  color: white !important;
}

/**** JE style ********/
.JE-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, rgb(247, 148, 29), rgb(217, 131, 26));
}

.JE-style #search-bar {
  background: #c46c00;
}

.JE-style .bloc-menu .link-menu {
  color: #ab5e00;
}

.JE-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #ffcf95fb;
  border-radius: 50px;
  box-shadow: 0 0.15rem 1.75rem 0 #ffa19a5c !important;
}

.JE-style .bloc-link .active-link {
  background: #ab5e00 !important;
  color: white !important;
}

/**** RM style ********/
.RM-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, rgb(71, 102, 255), rgb(50, 71, 179));
}

.RM-style #search-bar {
  background: #293da0;
}

.RM-style .bloc-menu .link-menu {
  color: #3247b3;
}

.RM-style .bloc-link .active-link {
  background: #3247b3 !important;
  color: white !important;
}

/**** MA style ********/
.MA-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, rgb(28, 55, 117), rgb(112, 130, 180));
}

.MA-style #search-bar {
  background: #7082b4;
}

.MA-style .link-menu {
  color: #1c3775;
}

.MA-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #bacdfd;
  box-shadow: 0 0.15rem 1.75rem 0 #c1d8ffbf;
}

.MA-style .bloc-link .active-link {
  background: #1c3775 !important;
  color: white !important;
}

/**** XX style ********/
.XX-style {
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15) !important;
  background: linear-gradient(123deg, hsla(230, 100%, 64%, 1) 0%, hsla(230, 100%, 64%, 1) 100%);
}

.XX-style #search-bar {
  background: #89ade780;
}

.XX-style .link-menu {
  color: #3247b3f0;
}

.XX-style .bloc-menu .link-menu.sub-link:not(.active-link):hover {
  background: #c4d6f4;
  box-shadow: 0 0.15rem 1.75rem 0 #c1d8ffbf;
}

.XX-style .bloc-link .active-link {
  background: #3247b3f0 !important;
  color: white !important;
  box-shadow: 0 0.15rem 1.75rem 0 #afc2e0ed !important;
}

/**** Table Style ******/
table {
  border: 2px solid black;
  border-radius: 15px !important;
  overflow: hidden;
  box-shadow: .20rem .20rem 1.70rem 0.20rem #3a3b4526 !important;
}

table.dataTable thead tr th {
  color: white;
}

.AC-content table.dataTable thead tr th {
  background-color: #d9291c !important;
}

.AP-content table.dataTable thead tr th {
  background-color: #0072ff !important;
}

.OP-content table.dataTable thead tr th {
  background-color: #f7ff14 !important;
  color: black;
}

.DS-content table.dataTable thead tr th,
.LA-content table.dataTable thead tr th {
  background-color: #191a1a !important;
}

.VX-content table.dataTable thead tr th,
.SP-content table.dataTable thead tr th,
.CY-content table.dataTable thead tr th,
.DG-content table.dataTable thead tr th,
.RM-content table.dataTable thead tr th,
.XX-content table.dataTable thead tr th {
  background-color: #4766ff !important;
}

.FT-content table.dataTable thead tr th {
  background-color: #2c818c !important;
}

.FO-content table.dataTable thead tr th {
  background-color: #9a172e !important;
}

.AH-content table.dataTable thead tr th {
  background-color: #e72020 !important;
}

.AR-content table.dataTable thead tr th {
  background-color: #ba0816 !important;
}

.JE-content table.dataTable thead tr th {
  background-color: #f7941d !important;
}

.MA-content table.dataTable thead tr th {
  background-color: #1c3775 !important;
}

mark {
  background-color: yellow;
}

.no-background {
  background: none !important;
}

.w-p-10 {
  width: 36px;
  height: 33px;
  font-size: 12px;
  opacity: .9;
  box-shadow: 0 0.15rem 1.75rem 0 rgb(58 59 69 / 14%) !important;
}

.settings optgroup {
  display: block !important;
  padding: 5px;
}

.settings optgroup option:first-child {
  padding-top: 5px;
}

.settings optgroup option {
  padding-bottom: 5px;
}

.sync-form .custom-select {
  min-height: 13rem;
}

#select-container {
  position: relative;
}

#loader {
  display: none;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #257bfa;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 2s linear infinite;
  position: absolute;
  top: 55%;
  margin-top: -10px;
  left: 50%;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

#overlay {
  display: none;
  position: absolute;
  top: 46px;
  left: 0;
  width: 100%;
  height: 82%;
  background-color: rgb(185 185 185 / 25%);
  z-index: 1000;
  border-radius: 5px;
}

#refImges {
  max-width: 4rem;
  margin-top: -3%;
}

.ref_trans_field {
  width: 93%;
}

/**** multi-fields *****/
.mr-2 {
  margin-right: .5rem !important;
}

.multi-fields-wrapper {
  background: #f9f9f9;
  padding: 5px 12px 15px 25px;
  border: 1px solid #e9e9e9;
  border-radius: 5px;
  max-height: 30rem;
  overflow: auto;
}

.custom-border-raduis {
  border-radius: 3px;
}

.custom-add-button {
  min-width: 115px;
  position: relative;
  left: 11px;
  font-size: 13px;
  font-weight: 600;
  min-height: 35px;
}

.custom-remove-button,
.custom-preview-button {
  min-width: 115px;
  font-size: 13px;
  font-weight: 600;
  min-height: 35px;
}

.custom-preview-button {
  border: 1px solid #eaeaea;
}

.custom-multifield-font-label {
  font-size: 13px;
  font-weight: 600 !important;
  color: #393939;
}

.custom-multi-field-title {
  font-size: 17px;
  position: relative;
  right: 8px;
  padding-top: 15px;
  padding-bottom: 0px;
  margin-bottom: 0px;
}

.multi-fields {
  position: relative;
}

.custom-hr {
  border-top: 1px solid #9b9797;
  margin: 20px 0px;
  width: 97%;
  position: relative;
  top: 2px;
}

.multi-fields-wrapper::-webkit-scrollbar {
  width: 12px;
  z-index: 9;
}

.multi-fields-wrapper::-webkit-scrollbar-track {
  background: none;
  border-radius: 8px;
}

.multi-fields-wrapper::-webkit-scrollbar-thumb {
  background: #6b6a6a !important;
  border-radius: 12px !important;
}

.multi-fields-wrapper::-webkit-scrollbar-thumb:hover {
  z-index: 9;
  background: #858282 !important;
}

.preview-media-block .custom-file input[type="file"] {
  display: none;
}

.preview-media-block .custom-file input[type="file"] {
  display: none;
}

.preview-media-block .custom-file label {
  padding: 10px 4px 10px 4px;
  background: #f9f9f9;
  display: table;
  color: #000738ad;
  min-width: 120px;
  margin-top: 10px;
  text-align: center;
  border-radius: 70px;
  font-size: 11px;
  border: 1px solid #cccccc80;
  font-weight: bold;
  margin-right: 1.25rem;
  height: 12px;
  cursor: pointer;
}

.preview-media-block .custom-file label::before {
  font-size: 11px;
  position: relative;
  right: 6px;
  top: -1px;
  content: "\f093";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.preview-close-style {
  outline: none;
  border: none;
}

.multi-fields-textarea {
  width: 100%;
  min-height: 40rem;
  border: 1px solid #eaeaea;
  color: #414141;
  background: #f1f1f1;
}

.multi-fields-textarea:active,
.multi-fields-textarea:focus {
  outline: none;
}

.search-input {
  min-height: 42px;
  font-size: 14px;
}

.preview-media-block .custom-file {}

.preview-media-multi-field {
  display: block;
  height: 50px;
  border: 1px solid #dcdcdc;
  padding: 1px;
}

.preview-media-delete {
  margin-top: 15px;
  margin-left: 15px;
  color: #dc3545;
  cursor: pointer;

}

.preview-media-block {
  position: relative;
  top: -15px;
}

.preview-media-name {
  font-size: 10px;
  position: relative;
  top: 6px;
  font-weight: 500;
  color: #565656;
}

.preview-image-modal ul.thumbnails.image_picker_selector li .thumbnail img {
  width: 310px;
  height: 224px;
}

.multi-fields-wrapper input {
  font-size: 15px;
}

.preview-image-modal .selected p {
  color: white;
  text-align: center;
}

.preview-image-modal .thumbnail p {
  text-align: center;
  position: relative;
  top: 10px;
  font-size: 15px;
}
.multi-fields .model-container:last-child hr {
  display: none !important;
}
/**** end multi-fields *****/

.fade:not(.show)
{
	opacity:1;
}
.iconpicker .iconpicker-item
{
	font-size:18px;
}

.badge-danger {
  color: #fff;
  background-color: #e74a3b;
}

.multi-fields-div .invalid-feedback {
  position: absolute;
  bottom: -5px;
}

.multi-fields-div .form-control.is-invalid {
  margin-bottom: 15px;
}

.multi-fields-div .form-check .invalid-feedback {
  position: relative !important;
  right: 20px !important;
  top: 3px !important;
}

.d-contents {
  display: contents;
}

div[multivalue="multiValue"] .invalid-feedback{
  display: inline-flex!important;
}

.environment-bar {
  padding: 6px;
}

.environment-bar span {
  color: white !important;
  padding: 5px;
  border-radius: 5px;
}

.environment-bar .env-dev {
  background: #0d9a12;
}

.environment-bar .env-develop {
  background: #0d9a12;
}

.environment-bar .env-prod {
  background: #e60d0d;
}

.environment-bar .env-preprod {
  background: #cccf02;
}

.environment-bar .env-integ {
  background: #da7e06;
}

/* ============= Tooltip overrides ============= */
.tooltip-container .tooltip-inner {
  text-align: left;
}

/* ============= Loader > ============= */
.loader {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  display: flex; /* Use flexbox layout */
  justify-content: center; /* Center horizontally */
  align-items: center; /* Center vertically */
}

.loader span {
  color: white;
  margin-top: 50%;
  
}

.loader .inner {
  width: 10px;
  height: 10px;
  margin: 3px;
  background-color: white;
  border-radius: 50%;
  animation: loader 1.2s infinite ease-in-out both;
}

.loader .one {
  animation-delay: -0.32s;
}

.loader .two {
  animation-delay: -0.16s;
}

.loader .three {
  animation-delay: 0s;
}

@keyframes loader {
  0%, 80%, 100% {
      transform: scale(0);
  }
  40% {
      transform: scale(1);
  }
}

/* Background overlay */
.loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); 
  z-index: 999; /* Ensure it's above the loader */
  display: none; /* Initially hidden */
}
/* ============= < Loader ============= */

/* ============= LowerTocamelcase ============= */
.lower-camel-case-label {
    color: grey; /* Make the text grey */
    font-size: 0.8em; /* Use a smaller font size */
  }
/* ============= LowerTocamelcase ============= */
.json-values {
  background: whitesmoke;
  max-height: 20rem;
  padding: 10px;
}

/* ============= < Dual-Box Important > ============= */
.bootstrap-duallistbox-container select {
  height: 200px !important;
  border-radius:4px;
}
.bootstrap-duallistbox-container .customButtonBox {
  margin-top: auto;
  margin-bottom: auto;
  padding-top: 105px;
}
.bootstrap-duallistbox-container .customButtonBox button {
    margin-bottom: 15px;
  }
.bootstrap-duallistbox-container .moveall, .bootstrap-duallistbox-container .removeall
{
	display: none;
}
.bootstrap-duallistbox-container .move, .bootstrap-duallistbox-container .remove
{
	color: #fff;
	border-color: #0d6efd;
	background-color: #0d6efd;
	font-size: 21px;
  font-weight: bold;
  width: 30% !important;
}
.bootstrap-duallistbox-container:not(.moveonselect) select
{
  border: var(--bs-border-width) solid #c7cdd3;
  border-radius: var(--bs-border-radius);
  border-top-left-radius: 4px !important;
  border-top-right-radius: 4px !important;
}



/* ============= < Dual-Box Important > ============= */
.bootstrap-duallistbox-container select {
  height: 200px !important;
  border-radius:4px;
}
.bootstrap-duallistbox-container .custom-button-box {
  margin-top: auto;
  margin-bottom: auto;
  padding-top: 105px;
}
.bootstrap-duallistbox-container .custom-button-box button {
    margin-bottom: 15px;
  }
.bootstrap-duallistbox-container .moveall, .bootstrap-duallistbox-container .removeall
{
	display: none;
}
.bootstrap-duallistbox-container .move, .bootstrap-duallistbox-container .remove
{
	color: #fff;
	border-color: #0d6efd;
	background-color: #0d6efd;
	font-size: 21px;
  font-weight: bold;
  width: 30% !important;
}
.bootstrap-duallistbox-container:not(.moveonselect) select
{
  border: var(--bs-border-width) solid #c7cdd3;
  border-radius: var(--bs-border-radius);
  border-top-left-radius: 4px !important;
  border-top-right-radius: 4px !important;
}

/* ============= <Global css as Mym > ============= */
.card-header
{
  background-color: #f8f9fc;
}
.card-footer
{
  background-color: #f8f9fc;
  border-top: 1px solid #e3e6f0;
}
#bo_access_tree
{
    border: solid 1px;
    padding: 12px;
}
 .far.fa-folder, .far.fa-folder-open{
  display: inline-block;
  font-size: 24px; 
  background: linear-gradient(to bottom, #f1c543, #bb583a);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.global_list img{
  width: 24px; 
  height: 24px; 
  mask-image: url('/images/plus.gif');
  mask-size: cover;
  mask-repeat: no-repeat;
  -webkit-mask-image: url('/images/plus.gif'); 
  -webkit-mask-size: cover; 
  -webkit-mask-repeat: no-repeat; 
  background-color: #dbb320;
}
.sections hr
{
  border-top: 2px solid #8c8b8b;
}
.menu-edge
{
  border:solid 1px; padding: 12px;
}

#loadingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Ensure it is on top of other elements */
}

#loadingIcon {
  animation: spin 1s linear infinite; /* Add a spinning animation */
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


/* Ensure the summary box takes the remaining space in the right table */
.summary-box {
    flex-grow: 1;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

#checkbox{
  padding: 8px; 
  width: 30px; 
  text-align: center;
}

#massiveOperation{
  display: inline-block; 
  width: auto; 
  -webkit-appearance: menulist;
}

.small-icon{
    width:48px;
    height:48px;
    margin-right: 1px;
}

.brand-icon{
    background-color:#707B7C ;
}

.img-flag {
    width: 48px;
    height: 48px;
    margin-right: 1px;
}


.page-item.disabled .page-link {
  color: #858796;
  pointer-events: none;
  cursor: auto;
  background-color: #fff !important;
  border-color: #dddfeb;
}

.page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: .35rem;
  border-bottom-left-radius: .35rem;
}

.page-link {
  position: relative;
  display: block;
  padding: .5rem .75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #4e73df;
  background-color: #fff;
  border: 1px solid #dddfeb;
}


/*****toast*****/
.toast {
  position: absolute;
  left: 60%;
  top: 0%;
  transform: translate(-60%, -100%);
  border-radius: 12px;
  background: #0d9a12;
  padding: 20px 35px 20px 25px;
  box-shadow: 0 6px 20px -5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.35);
  opacity: 0.8;
}

.toast.active {
  transform: translate(-60%, 5.2rem);
}

.toast .toast-content {
  display: flex;
  align-items: center;
}

.toast-content .check {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 35px;
  min-width: 35px;
  background-color: #00661b;
  color: #fff;
  font-size: 20px;
  border-radius: 50%;
}

.toast-content .message {
  display: flex;
  flex-direction: column;
  margin: 0 20px;
}

.message .text {
  font-size: 16px;
  font-weight: 400;
  color: #666666;
}

.message .text.text-1 {
  font-weight: 600;
  color: #fff;
}

.toast .close {
  position: absolute;
  top: 10px;
  right: 15px;
  padding: 5px;
  cursor: pointer;
  opacity: 0.7;
}

.toast .close:hover {
  opacity: 1;
}

.toast .progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  width: 100%;
  background: #00661b;

}

.toast .progress:before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: #0d9a12;
}

.progress.active:before {
  animation: progress 3s linear forwards;
}

@keyframes progress {
  100% {
    left: 100%;
  }
}

body{
  overflow-x: hidden;
}

.btn-style-custom a, .btn-style-custom button {
  font-size: 17px;
  height: auto;
  margin: 0;
  font-weight: 500;
}

.modal-header .close {
  outline: none;
  border: 1px solid #eee;
  color: #1b1b1b;
}
.content-detail-file{
  background: none !important;
}

.is-error .uppy-DashboardItem-action--remove {
  display: block !important;
}

.uppy-DashboardItem-progressIndicator{
  display: none!important;
}

.dialog-ovelay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.50);
  z-index: 999999
}
.dialog-ovelay .dialog {
  width: 400px;
  margin: 100px auto 0;
  background-color: #fff;
  box-shadow: 0 0 20px rgba(0,0,0,.2);
  border-radius: 3px;
  overflow: hidden
}
.dialog-ovelay .dialog header {
  padding: 10px 8px;
  background-color: #f6f7f9;
  border-bottom: 1px solid #e5e5e5
}
.dialog-ovelay .dialog header h3 {
  font-size: 14px;
  margin: 0;
  color: #555;
  display: inline-block
}
.dialog-ovelay .dialog header .fa-close {
  float: right;
  color: #c4c5c7;
  cursor: pointer;
  transition: all .5s ease;
  padding: 0 2px;
  border-radius: 1px    
}
.dialog-ovelay .dialog header .fa-close:hover {
  color: #b9b9b9
}
.dialog-ovelay .dialog header .fa-close:active {
  box-shadow: 0 0 5px #673AB7;
  color: #a2a2a2
}
.dialog-ovelay .dialog .dialog-msg {
  padding: 12px 10px
}
.dialog-ovelay .dialog .dialog-msg p{
  margin: 0;
  font-size: 15px;
  color: #333
}
.dialog-ovelay .dialog footer {
  border-top: 1px solid #e5e5e5;
  padding: 8px 10px
}
.dialog-ovelay .dialog footer .controls {
  direction: rtl
}
.dialog-ovelay .dialog footer .controls .button {
  padding: 5px 15px;
  border-radius: 3px;
  font-size: 13px;
}
.button {
cursor: pointer
}
.button-default {
  background-color: rgb(248, 248, 248);
  border: 1px solid rgba(204, 204, 204, 0.5);
  color: #5D5D5D;
}
.button-danger {
  background-color: #f44336;
  border: 1px solid #d32f2f;
  color: #f5f5f5
}
.link {
padding: 5px 10px;
cursor: pointer
}

.existing-name {
    font-size: 14px;
    color: #7a0101;
    font-weight: 400;
}

/* bulkoperations  */
/* CSS class for truncating text with ellipsis */
#dtLocalTranslations .text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0px
}

/* Ensure the table's column has a specific width if necessary */
.table .truncated-column {
  max-width: 50px;
  /* Adjust based on your needs */
}

.tables-container {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  width: 100%;
  overflow: hidden;
}

.left-table {
  flex: 2 1 66.66%;
  display: flex;
  flex-direction: column;
}

.right-table {
  flex: 1 1 33.33%;
  display: flex;
  flex-direction: column;
}

#summary_box #brandImges, #dtLocalTranslations #brandImges {
  height: 25px;
  width: 25px;
  background-color: rgb(100 100 100);
}

#summary_box #couImges, #dtLocalTranslations #couImges {
  height: 25px;
  width: 25px;
}

#dt_demolocal_list #brandImges {
  height: 50px;
  width: 50px;
  background-color: rgb(197 176 176);
}

#dt_demolocal_list #couImges {
  height: 50px;
  width: 50px;
}

#word-prevent
{
  white-space: normal!important;
  word-wrap: normal!important;
  word-break: keep-all!important;
}
.jstree-themeicon.far.fa-folder, .jstree-themeicon.far.fa-folder-open{
  display: inline-block;
  font-size: 24px;
  background: linear-gradient(to bottom, #f1c543, #bb583a);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin: 0px 5px;
  width: 27px !important;
}

.jstree-node {
  margin-bottom: 0!important;
}
.jstree-node {
  min-height: 32px !important;
  line-height: 32px !important;
  margin-left: 0px !important;
  min-width: 32px !important;
}
.jstree .jstree-open .jstree-children {
  position: relative;
  top: 5px;
  margin-left: 20px;
}
#using_json{
  margin-top: 25px;
  padding-left: 10px;
}

.color-add{
  color: #1cc88a;
  padding: 4px;
}

#using_json a.jstree-anchor {
  text-transform: uppercase;
}
#using_json .jstree-loading a.jstree-anchor {
  text-transform: capitalize;
}

#massiveOperation
{
  background-image: none !important;
}

/* ///////merge custom css /////////*/
.mandatory {
  color: red;
}
.file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.image-card {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: hsl(0, 4%, 5%);
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2), 0 6px 20px rgba(0, 0, 0, 0.19);
  transition: transform 0.3s;
}

.image-card:hover {
  transform: translateY(-5px) scale(1.1);
}

.image-card img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3));
  transition: transform 0.3s;
}

.image-card:hover img {
  transform: scale(1.1);
}

.path {
  font-size: 19px;
  color: #666;
  margin-top: 5px;
}

.gray-box {
  background-color: hwb(0 96% 3%);
  /* Gray background */
  justify-content: center;
  /* Center horizontally */
  align-items: center;
  /* Center vertically */
  display: flex;
  padding: 5px;
  /* Add padding to the box */
}

.button-34 {
  background: #5e5df0;
  border-radius: 999px;
  box-shadow: #5e5df0 0 10px 20px -10px;
  box-sizing: border-box;
  color: #ffffff;
  cursor: pointer;
  font-family: Inter, Helvetica, "Apple Color Emoji", "Segoe UI Emoji", NotoColorEmoji, "Noto Color Emoji", "Segoe UI Symbol", "Android Emoji", EmojiSymbols, -apple-system, system-ui, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", sans-serif;
  font-size: 20px;
  /* Increased font size */
  font-weight: 700;
  line-height: 24px;
  opacity: 1;
  outline: 0 solid transparent;
  padding: 12px 24px;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
  width: fit-content;
  word-break: break-word;
  border: 0;
}

.status-wrapper {
  margin-top: 25px;
}

.status-wrapper h5 {
  margin-bottom: 0;
}

.status {
  font-size: 17px;
  padding: 5px;
  border-radius: 0px;
  margin-bottom: 2;
  margin-top: -26px;
  text-transform: capitalize;
}

/* Optional: Add padding to pre elements */ 

 pre {
    padding: 20px;
    font-size: 16px !important;
} 

/* Custom styles for specific statuses */
.status.released {
  color: #28a745;
  /* Green color for "released" status */
}

.status.imported {
  color: red;
  /* Blue color for "imported" status */
}

.status.updated {
  color: rgb(218, 112, 20);
  /* Yellow color for "updated" status */
}

#select-media-modal .upload_media .bloc_three,
.media-page .bloc_three {
    border: 1px solid #e3e6f0;
}

.media-page .bloc_three, .media-page .bloc_file{
    box-shadow: 0 .15rem 1.75rem 0 rgb(195 195 195 / 15%) !important;
}

.media-page .bloc_file #files .cadre_image #list-image .col-3 .cadre-image{
    border-radius: 3px;
}

#select-media-modal .upload_media .bloc_three .global_list,
.media-page .bloc_three .global_list {
    margin-top: 25px;
    padding-left: 10px;
    color: black;
}

#select-media-modal .upload_media .bloc_three ul,
.media-page .bloc_three ul {
    list-style: none;
}

#select-media-modal .upload_media .bloc_three ul li,
.media-page .bloc_three ul li {
    cursor: pointer;
}

#select-media-modal .upload_media .bloc_file,
#select-media-modal .upload_media .bloc_file #files .cadre_image,
.media-page .bloc_file,
.media-page .bloc_file #files .cadre_image {
    border: 1px solid #e3e6f0;
}

#select-media-modal .upload_media .bloc_file #files .cadre_image p,
.media-page .bloc_file #files .cadre_image p {
    margin-top: -18px;
    width: fit-content;
    padding: 5px;
    background: white;
    margin-left: 5px;
}

#select-media-modal .upload_media .bloc_file #files .cadre_image #list-image .col-3,
.media-page .bloc_file #files .cadre_image #list-image .col-3 {
    max-height: 125px;
    cursor: pointer;
}

#select-media-modal .upload_media .bloc_file #files .cadre_image #list-image .col-3 .cadre-image,
.media-page .bloc_file #files .cadre_image #list-image .col-3 .cadre-image {
    border: 1px solid #e3e6f0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

#select-media-modal .upload_media .bloc_file #files .cadre_image #list-image .col-3 .cadre-image:hover,
.media-page .bloc_file #files .cadre_image #list-image .col-3 .cadre-image:hover,
.media-page .bloc_file #files .cadre_image #list-image .col-3 .cadre-image.selected {
    border: 2px solid #4e73df;
}

#select-media-modal .upload_media .bloc_file #files .cadre_image #list-image .col-3 .cadre-image img,
.media-page .bloc_file #files .cadre_image #list-image .col-3 .cadre-image img {
    max-width: 100%;
    max-height: 100%;
}

.media-page .bloc_file #files .cadre_image #list-image .col-3 .cadre-image .fa-search {
    position: absolute;
    top: 7px;
    right: 10px;
}

@keyframes spinLoading {
  from {
      transform: rotate(0deg);
  }
  to {
      transform: rotate(360deg);
  }
}

.media-page .bloc_file #files .cadre_image .BlocLoading {
  text-align: center;
  padding: 15px;
}

#alert-loading .loading,
.media-page .bloc_file #files .cadre_image .BlocLoading {
  animation: spinLoading 2000ms infinite linear;
}

.page-text {
  display: none;
}

.btn-wrapper {
  margin-right: 10px;
}

.media-info .media-formats {
  cursor: pointer;
}

.tooltip {
  display: none;
  background-color: #333;
  color: #fff;
  padding: 0.5rem;
  border-radius: 0.25rem;
  z-index: 10;
}

.tooltip.show {
  display: block;
}



.nav-tabs .nav-link:hover {
  background-color: #e2e6ea;
  color: #007bff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  /* Slightly increase shadow on hover */
  transform: translateY(-2px);
  /* Lift the tab slightly on hover */
}

.nav-tabs .nav-link:focus {
  outline: none;
  /* Remove focus outline */
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.5);
  /* Add custom focus outline */
}

.json-container {
  margin-top: 50px; 
  padding: 20px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  max-height: auto; 
  overflow: auto; 
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
}

.icon-view {
  background-color: #333; 
  display: inline-block; 
  margin-left: 50px;
}

#flag-icon {
  max-width: 60px; 
  max-height: 60px; 
  object-fit: cover;
}

.diff-title{
  font-size: 20px; 
  color: #6c757d;
}

.back-btn{
  font-size: 24px; 
}

.filename{
  margin-left: 10px; 
}

.file-title{
   font-size: 24px;
}

.card {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-header {
  margin: 0;
  padding: 10px 15px;
  /* background-color: #007bff; */
  /* color: white; */
}

#list-image.row {
  justify-content: normal!important;
}

#list-image .mb-2 {
  margin-bottom: 1.5rem !important;
}
.media-page .bloc_file #files .cadre_image #list-image .col-3 .cadre-image{
  position: relative !important;
}

#publish-page .row,
#settings-page .row {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

#publish-page .nav-tabs,
#settings-page .nav-tabs  {
  border-bottom: none;
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

#publish-page .nav-tabs .nav-item,
#settings-page .nav-tabs .nav-item {
  margin: 0 10px;
}

#publish-page .nav-tabs .nav-link,
#settings-page .nav-tabs .nav-link {
  border-radius: 30px;
  padding: 10px 20px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #495057;
  font-size: 16px;
  font-weight: bold;
  transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out, transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#publish-page .nav-tabs .nav-link.active,
#settings-page .nav-tabs .nav-link.active {
  background-color: #007bff;
  color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-color: #007bff;
  transform: scale(1.1);
}

#publish-page .nav-tabs .nav-link:hover,
#settings-page .nav-tabs .nav-link:hover {
  background-color: #e2e6ea;
  color: #007bff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

#publish-page .nav-tabs .nav-link:focus ,
#settings-page .nav-tabs .nav-link:focus{
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.5);
}