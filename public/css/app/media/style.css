.media-container>* {
  color: #212529;
}

.media-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: 4.3rem minmax(58px, min-content) fit-content(80vh) 1fr;
  box-shadow: 3px 2px 15px #e5e5e5;
  padding: 0;
  margin: 5px;
  min-height: 84vh;
  grid-auto-rows: 0px;
}

.header {
  grid-column: 1 / -1;
  padding: 0rem .8rem;
  background: linear-gradient(90deg, rgb(228 228 228) 0%, rgba(231, 231, 231, 1) 46%, rgb(243 243 243) 100%);
  border: 1px solid #d8d8d8;
}

.sub-header {
  grid-column: 2 / -1;
  padding: 0rem .8rem;
  background: #f4f4f499;
  border-bottom: 1px solid #d8d8d8;
  border-right: 1px solid #d8d8d8;
}

.sidebare {
  grid-column: 1 / 2;
  grid-row: 2 / -1;
  background: #f4f4f499;
  border-right: 1px solid #d8d8d8;
  border-left: 1px solid #d8d8d8;
  border-bottom: 1px solid #d8d8d8;
  overflow: auto;
  height: 77vh;
}

.media-content {
  grid-row: 3 / -1;
  grid-column: 2 / -1;
  border-right: 1px solid #d8d8d8;
  border-bottom: 1px solid #d8d8d8;
  background-color: #fcfcfc;
}

.card-box {
  padding: 13px 0px 10px 15px;
  border-radius: 3px;
  background-color: #fcfcfc;
}

.file-man-box {
  padding: 15px;
  border: 2px solid #e3eaef;
  border-radius: 5px;
  position: relative;
  margin-bottom: 20px;
  transition: all 0.3s;
  transition-timing-function: ease-in-out;
  cursor: pointer;
}

.file-man-box .file-close {
  color: #f1556c;
  position: absolute;
  line-height: 24px;
  font-size: 24px;
  right: 10px;
  top: 10px;
  visibility: hidden
}

.file-man-box .file-img-box {
  line-height: 120px;
  text-align: center;
  margin-bottom: 1.5rem;
  margin-top: 1.5rem;
}

.file-man-box .file-img-box img {
  height: 64px;
  width: 100%;
  object-fit: contain;
}

.file-man-box .file-download {
  font-size: 25px;
  color: #98a6ad;
  position: absolute;
  right: 10px
}

.file-man-box .file-download:hover {
  color: #313a46
}

.file-man-box .file-man-title {
  padding-right: 25px;
  color: #212529;
  line-height: 1.5rem;
}

.file-man-box:hover {
  -webkit-box-shadow: 0 0 24px 0 rgba(0, 0, 0, .06), 0 1px 0 0 rgba(0, 0, 0, .02);
  box-shadow: 0 0 24px 0 rgba(0, 0, 0, .06), 0 1px 0 0 rgba(0, 0, 0, .02);
  border: 2px solid #0d6efd99;
}

.file-man-box.selected {
  -webkit-box-shadow: 0 0 24px 0 rgba(0, 0, 0, .06), 0 1px 0 0 rgba(0, 0, 0, .02);
  box-shadow: 0 0 24px 0 rgba(0, 0, 0, .06), 0 1px 0 0 rgba(0, 0, 0, .02);
  border: 2px solid #0d6efd99;
}

.file-man-box:hover .file-close {
  visibility: visible
}

.text-overflow {
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  width: 100%;
  overflow: hidden;
}

h5 {
  font-size: 15px;
}

.btn-actions {
  border: none;
  padding: 5px 16px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 2px;
  min-width: 5rem;
  box-shadow: 6px 2px 15px #dcdcdc;
}

.icon-explore {
  background: hsl(209.6deg 33.57% 30.77%);
  padding: 6px 8px 6px 12px;
  color: white;
  border-radius: 3px;
}

#custom-search-input {
  padding: 3px;
  border: solid 1px #E4E4E4;
  border-radius: 23px;
  background-color: #f9f9f9;
}

#custom-search-input input {
  border: 0;
  box-shadow: none;
  z-index: auto;
  padding: 3px 3px 3px 10px;
  font-size: 15px;
  background: #f9f9f9;
}

#custom-search-input button {
  margin: 2px 0 0 0;
  background: none;
  box-shadow: none;
  border: 0;
  color: #666666;
  padding: 0 8px 0 10px;
  border-left: solid 1px #ccc;
  min-width: 3rem;
  height: 85%;
}

#custom-search-input button:hover {
  border: 0;
  box-shadow: none;
  border-left: solid 1px #ccc;
}

#custom-search-input .glyphicon-search {
  font-size: 16px;
  position: unset;
  color: inherit;
}

.custom-border {
  border-right: 1px solid #c8c8c8c4;
  border-left: 1px solid #c8c8c8c4;
}

.header__right__btn__color {
  color: #666666;
  min-width: 4.5rem;
  text-align: center;
}

.refresh__btn {
  min-width: 2.5rem;
  text-align: center;
  padding: 0;
}

.title-header {
  color: #1b3762;
  font-weight: 700;
}

.fs-px-14 {
  font-size: 14px;
}

.media-content .content {
  background-color: #fcfcfc;
}

.right__header>a {
  font-size: 13px;
  color: #334b63;
  background: #2d8256;
  padding: 10px;
  color: white;
  border-radius: 5px;
  box-shadow: 9px 8px 12px #dcdcdc;
}

.sidebare::-webkit-scrollbar-thumb {
  background: #7b848dd7 !important;
  border-radius: 12px !important;
  margin-top: 5px;
  margin-bottom: 5px;
}

.sidebare::-webkit-scrollbar-thumb:hover {
  z-index: 9;
  background: #727a82 !important;
}

.sidebare::-webkit-scrollbar {
  width: 10px;
  z-index: 9;
}

.sidebare::-webkit-scrollbar-track {
  background: rgb(241, 241, 241);
  border-radius: 8px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.header-title {
  font-size: 1.1rem;
  padding-bottom: 5px;
  color: #000b2b;
}

.media-content__full {
  grid-row: 2 / -1;
}

.d-none__not__important {
  display: none;
}

.list-files__content {
  justify-content: normal;
}

.modal-body-folder {
  padding: 1rem;
}

.footer-pagination {
  bottom: 0;
  width: 100%;
  left: 0;
  background: #f2f2f2;
  padding: 1rem;
  border-top: 1px solid #d8d8d8;
}

.footer-pagination a {
  font-size: 0.9rem;
}

.show-modal-label {
  border: none !important;
  text-underline-offset: 3px;
  font-weight: 600 !important;
  color: #777779;
}

#show-modal-image {
  background-image: url("../../../images/media/default-img.png");
  background-position: center;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  transition: .5s ease;
  background-size: cover;
  filter: blur(2px);
}

.show-modal-overlay {
  left: 0;
  top: 0;
  right: 0;
  background-color: #0e0e0ed1;
  opacity: 1;
  transition: .5s ease;
  z-index: 9;
}

.show-modal-preview-btn, .show-modal-download-btn{
  min-width: 10rem;
  font-weight: 800;
  font-size: 14px;
  opacity: 1;
  transition: .5s ease;
}

.show-modal-col-image:hover .show-modal-overlay {
  opacity: 1;
}

.show-modal-col-image:hover .show-modal-preview-btn {
  opacity: 1;
  /*top: 50%;*/
}

.show-modal-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
  width: auto;
}

.show-modal-col-info {
  background-color: #f5f5f5;
}

#show-modal-close-btn,
#preview-modal-close-btn {
  top: -1.7rem;
}

#preview-media-modal {
  background-color: #090909d6;
  z-index: 999999;
}

#preview-modal-image {
  box-shadow: 1px 1px 8px #2f2e2e;
}

#preview-modal-image>img, #preview-modal-image>video {
  width: 100%;
  max-height: 50rem;
  min-width: 10rem;
  min-height: 10rem;
}

.empty-search-icon {
  color: #6a6e72 !important
}

.empty-search-description {
    max-width: 15rem;
    color: #8f8f8f;
}

.empty-search-title {
  font-size: 1.2rem;
  color: #494949;
}

.media-content-files{
  overflow: auto;
  max-height: 37rem;
  margin-right: -16px;
}

.footer-pagination, .footer-pagination > *, .media-content > *{
  transition: .7 ease-out;
  transition: .7 ease-in-out;
}

.media-content-files::-webkit-scrollbar-thumb {
  background: #7b848dd7 !important;
  border-radius: 12px !important;
  margin-top: 5px;
  margin-bottom: 5px;
}

.media-content-files::-webkit-scrollbar-thumb:hover {
  z-index: 9;
  background: #727a82 !important;
}

.media-content-files::-webkit-scrollbar {
  width: 10px;
  z-index: 9;
}

.media-content-files::-webkit-scrollbar-track {
  background: rgb(241, 241, 241);
  border-radius: 8px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.no-folder__selected__title, .no-file__existed__title {
  font-size: 1.2rem;
  color: #494949;
}

.no-folder__selected__paragraph, .no-file__existed__paragraph {
  color: #8f8f8f;
}

.show-edit-folder {
  cursor:pointer;
  color:#15aabf;
}

.show-delete-folder{
  cursor:pointer;
  color:#B0413E;
}

.animate__fadeOutDown {
  -webkit-animation-name: fadeOutDown;
  animation-name: fadeOutDown;
  animation-duration: .5s;
}

@-webkit-keyframes fadeOutDown {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
@keyframes fadeOutDown {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}

@-webkit-keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
  animation-duration: .5s;
}

#show-modal-label-media-title{
  word-break: break-all;
}