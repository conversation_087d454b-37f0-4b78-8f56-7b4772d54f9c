{% extends '_layout/layout.html.twig' %}

{% block title %}
	{{ 'widget_management' | trans | capitalize }}
{% endblock %}

{% block content %}
	<div class="card card-primary widget-info">
		{% include '_layout/form_card_header.html.twig' with{
        'title': 'widgets' | trans | capitalize
    }%}
		<div class="card-body p-4">
			<div class="row g-4">
				{# Widget Information #}
				<div class="col-md-6">
					<div class="info-box bg-light shadow-sm rounded">
						<div class="info-box-content p-3">
							<span class="info-box-text fs-5 fw-bold mb-3 border-bottom pb-2 text-primary">{{ 'widget_information' | trans | upper }}</span>
							<ul class="list-group list-group-flush">
								<li class="list-group-item py-3 fs-5">
									<b class="me-2">{{ 'name' | trans | capitalize }}:</b>
									<span class="text-dark">{{ widget.name }}</span>
								</li>
								<li class="list-group-item py-3 fs-5">
									<b class="me-2">{{ 'type' | trans | capitalize }}:</b>
									<span class="text-dark">{{ widget.type }}</span>
								</li>
								<li class="list-group-item py-3 fs-5">
									<b class="me-2">{{ 'version' | trans | capitalize }}:</b>
									<span class="text-dark">{{ widget.version }}</span>
								</li>
								<li class="list-group-item py-3 fs-5">
									<b class="me-2">{{ 'description' | trans | capitalize }}:</b>
									<span class="text-dark">{{ widget.description }}</span>
								</li>
							</ul>
						</div>
					</div>
				</div>

				{# Widget Data Information #}
				<div class="col-md-6">
					<div class="info-box bg-light shadow-sm rounded">
						<div class="info-box-content p-3">
							<span class="info-box-text fs-5 fw-bold mb-3 border-bottom pb-2 text-primary">{{ 'widget_data_information' | trans | upper }}</span>
							<div class="card border-0">
								<ul class="list-group list-group-flush">
									{% if widgetData|length > 0 %}
										<li class="list-group-item py-3 fs-5">
											<b class="me-2">{{ 'brand' | trans | capitalize }}:</b>
											<span class="text-dark">{{ widgetData[0].brand.name }}</span>
										</li>
										<li class="list-group-item py-3 fs-5">
											<b class="me-2">{{ 'country' | trans | capitalize }}:</b>
											<span class="text-dark">{{ widgetData[0].country.name }}</span>
										</li>
										<li class="list-group-item py-3 fs-5">
											<b class="me-2">{{ 'source' | trans | capitalize }}:</b>
											{% for widgetData in widgetData %}
												<span class="badge bg-info me-2 p-2 fs-6">{{ widgetData.source }}</span>
											{% endfor %}
										</li>
										<li class="list-group-item py-3 fs-5">
											<b class="me-2">{{ 'enabled' | trans | capitalize }}:</b>
											{% for widgetData in widgetData %}
												<span class="badge {% if widgetData.enabled %}bg-success{% else %}bg-danger{% endif %} me-2 p-2 fs-6">
													{{ widgetData.enabled ? '✓' : '✗' }}
												</span>
											{% endfor %}
										</li>
									{% else %}
										<li class="list-group-item py-3 fs-5 text-muted">{{ 'no_widget_data' | trans }}</li>
									{% endif %}
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="card card-primary card-outline card-outline-tabs mt-2">
		<div class="card-header p-0 border-bottom-0">
			<ul class="nav nav-tabs" id="nav-tab" role="tablist">
				{% for source, form in forms %}
					<li class="nav-item">
						<a class="nav-link {% if currentSource == source or (forms[currentSource] is not defined and loop.index == 1) %} active {% endif %}" id="nav-{{source}}-tab" data-toggle="tab" href="#nav-{{source}}" role="tab" aria-controls="nav-{{source}}" aria-selected="true">{{source}}</a>
					</li>
				{% endfor %}
			</ul>
		</div>
		<!-- /.card-header -->
		<div class="card-body">
			<div class="tab-content" id="nav-tabContent">
				{% for source, featureForms in forms %}
					<div class="tab-pane fade {% if currentSource == source or (forms[currentSource] is not defined and loop.index == 1) %} show active {% endif %}" id="nav-{{source}}" role="tabpanel" aria-labelledby="nav-{{source}}-tab">
						<div class="card">
							<div class="card-body">
								<div class="row">
									<div class="col-md-6 enabeled-msg"></div>
								</div>
								<div class="row">
									<div class="col-md-3">
										<div class="form-group mt-3">
											<input class="widget_enabled" type="checkbox" value="{{isWidgetEnabled[source]}}" id="widget_activation_{{source}}" data-source="{{source}}" {% if isWidgetEnabled[source] == 1 %} checked {% endif%}>
											<label class="form-check-label" for="widget_activation_{{source}}">
												{{ 'enabled' |trans }}
											</label>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="card mt-3">
							<div class="card-header d-flex justify-content-between align-items-center">
								<span>{{'features'|trans}}</span>
								<button type="button" id="save-all-features-{{source}}" class="btn btn-success save-all-features" data-source="{{source}}" style="display: none;">
									<i class="fas fa-save"></i> {{ 'widget_save_all_features'|trans == 'widget_save_all_features' ? 'Save All Features' : 'widget_save_all_features'|trans }}
								</button>
							</div>
							<div class="card-body">
								<div class="row mt-3">
									<div class="col-5 col-sm-3">
										<div class="left-tabs nav flex-column nav-tabs h-100" id="vert-tabs-tab-{{source}}" role="tablist" aria-orientation="vertical">

											{% for feature, form in featureForms %}
												<a class=" vertical-nav-link nav-link {% if loop.index == 1 %} active {% endif %}" id="vert-tabs-{{source}}-{{feature}}-tab" data-toggle="pill" href="#vert-tabs-{{source}}-{{feature}}" role="tab" aria-controls="vert-tabs-{{source}}-{{feature}}" aria-selected="false">{{form.title}}</a>
											{% endfor %}
										</div>
									</div>
									<div class="col-7 col-sm-9">
										<div class="tab-content" id="vert-tabs-tabContent-{{source}}">
											{% for feature, form in featureForms %}

												<div class="tab-pane text-left fade {% if loop.index == 1 %} show active {% endif %}" id="vert-tabs-{{source}}-{{feature}}" role="tabpanel" aria-labelledby="vert-tabs-{{source}}-{{feature}}-tab">
													{% set form = form.form %}
													{{ form_start(form, { 'action': "?source="~source, 'attr': {'class': 'form-horizontal feature-form', 'data-source': source, 'data-feature': feature}}) }}
													{{ form_errors(form) }}
													{% set form_view = indexbysection(form) %}
													{% for section, rows in form_view %}
														{% if section != "default" and section is not empty %}
															<h5 class="">{{section|trans}}</h5>
														{% endif %}
														{% for language, row in rows %}
															{% for field in row %}
																{{ include('widget/media_model.html.twig', {'prefix': field.vars.id}) }}

																<div class="multi-fields" data-section="{{field.vars.id}}">
																	{% if field.vars.row_attr.isMultifield is defined and field.vars.row_attr.isMultifield == true %}
																		{% include "feature/multi_fields.html.twig" %}
																	{% else %}
																		{% if 'hidden' not in field.vars.block_prefixes %}
																			<div class="row">
																				{% if form_label(field) is not empty %}
																					<div class="col-md-3">
																						{{ form_label(field, null, {'label_attr': {'class': 'feature-label d-contents'}}) }}

																						{% if language != "default" and isMultilanguageSite %}
																							<img src="{{asset('images/flags/'~language~'.svg')}}" style=" max-width: 2rem;">
																						{% endif %}
																					</div>
																				{% endif %}
																				<div class="{{ form_label(field) is empty ? 'col-md-4' : 'col-md-6' }}">
																					{% if field.vars.row_attr.type is defined and (field.vars.row_attr.type == 'mediaType' or field.vars.row_attr.type == 'fileInput')%}
																						<div class="preview-media-block d-flex">
																							{% set url = field.vars.name|replace({ '_input' : ''}) %}
																							{{ form_widget(field, { required: false }) }}
																							{% if field.parent.vars.data[url] is defined and field.parent.vars.data[url] is not empty %}
																								<div class="preview-block-image">
																									<div class="d-flex">
																										<a class="preview-media-link" target="_blank" href="{{field.parent.vars.data[url]}}">
																											<img src="{{field.parent.vars.data[url]}}" class="preview-media-multi-field" alt="">
																										</a>
																										<i class="fas fa-trash preview-media-delete"></i>
																									</div>
																									<span class="preview-media-name">{{field.parent.vars.data[url] |split('/')|last}}</span>
																								</div>
																							{% endif %}
																						</div>
																					{% else %}
																						<div class="col-md-7 mb-3">
																							{{ form_widget(field) }}
																						
																						</div>
																					{% endif %}
																				</div>
																			</div>
																		{% endif %}
																	{% endif %}
																</div>
															{% endfor %}
														{% endfor %}
													{% endfor %}
													<div class="text-right mt-0">
														<a href="{{ path('widget_management_list') }}" class="btn btn-danger">Cancel</a>
														<button class="btn btn-primary float-right feature-save-btn" type="submit" data-source="{{source}}" data-feature="{{feature}}">{{ button_label|default('save')|trans }}</button>
													</div>
													{{ form_end(form) }}
												</div>
											{% endfor %}
										</div>
										<!-- /.tab-content (source) -->
									</div>
								</div>
							</div>
						</div>

						<div class="card mt-3">
							<div class="card-header">{{'labels'|trans}}</div>
							<div class="card-body">
								<table class="table table-bordered">
									<thead>
										<th>{{'label'|trans}}</th>
										<th>{{'local_translation'|trans}}</th>
										<th>{{'reference_translation'|trans}}</th>
										<th>{{'action'|trans}}</th>
									</thead>
									<tbody>
										{% for label in labels[source] %}
											<tr>
												<td>{{label.label}}</td>
												<td>{{label.localTrans}}</td>
												<td>{{label.referenceTrans}}</td>
												<td>
													<a href="{{path('local_translation_edit', {'id': label.id})}}" class="btn btn-warning" target="_blank">
														<i class="fas fa-edit"></i>
													</a>
												</td>
											</tr>
										{% endfor %}
									</tbody>
								</table>
							</div>
						</div>
						{% if s3Resources[source] is defined %}
							{{ include('widget/widget_s3_resources.html.twig', {'s3Resources': s3Resources[source]}) }}
						{% endif %}
						<!-- /.card-body -->
					</div>
					<!-- /.tab-pane -->
				{% endfor %}
				<div class="card mt-3">
					{{ include('widget/accessLog.html.twig', {'accessLogs': accessLogs}) }}
				</div>
			</div>
		</div>
	{% endblock %}

	{% block script %}
		<script type="text/javascript">
			// Save All Features functionality
			$(document).ready(function() {
				var savedForms = {};
				var widgetId = {{ widget.id }};

				// Show Save All button when any form has data
				function showSaveAllButton(source) {
					$('#save-all-features-' + source).show();
				}

				// Store form data in session storage
				function storeFormData(source, feature, formData) {
					var key = 'widget_' + widgetId + '_' + source;
					var storedData = JSON.parse(sessionStorage.getItem(key) || '{}');
					storedData[feature] = formData;
					sessionStorage.setItem(key, JSON.stringify(storedData));
					savedForms[source] = storedData;
					showSaveAllButton(source);
				}

				// Get all stored form data for a source
				function getStoredFormData(source) {
					var key = 'widget_' + widgetId + '_' + source;
					return JSON.parse(sessionStorage.getItem(key) || '{}');
				}

				// Clear stored data for a source
				function clearStoredData(source) {
					var key = 'widget_' + widgetId + '_' + source;
					sessionStorage.removeItem(key);
					delete savedForms[source];
					$('#save-all-features-' + source).hide();
				}

				// Handle individual form submissions
				$('.feature-form').on('submit', function(e) {
					e.preventDefault();
					var form = $(this);
					var source = form.data('source');
					var feature = form.data('feature');
					var formData = form.serialize();

					// Store form data
					storeFormData(source, feature, formData);

					// Show success message
					var successMsg = '<div class="alert alert-success alert-dismissible fade show" role="alert">' +
						'<i class="fas fa-check"></i> Feature "' + feature + '" data saved locally. Click "Save All Features" to submit all changes.' +
						'<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
						'</div>';

					// Remove existing alerts and add new one
					form.find('.alert').remove();
					form.prepend(successMsg);

					// Auto-dismiss after 3 seconds
					setTimeout(function() {
						form.find('.alert').fadeOut();
					}, 3000);
				});

				// Handle Save All Features button
				$('.save-all-features').on('click', function() {
					var button = $(this);
					var source = button.data('source');
					var storedData = getStoredFormData(source);

					if (Object.keys(storedData).length === 0) {
						alert('No feature data to save. Please fill and save individual features first.');
						return;
					}

					// Disable button and show loading
					button.prop('disabled', true);
					var originalHtml = button.html();
					button.html('<i class="fas fa-spinner fa-spin"></i> Saving...');

					// Submit all forms via AJAX
					var promises = [];
					var currentUrl = window.location.href.split('?')[0];

					$.each(storedData, function(feature, formData) {
						var promise = $.ajax({
							url: currentUrl + '?source=' + source,
							type: 'POST',
							data: formData + '&combined=1',
							dataType: 'json'
						});
						promises.push(promise);
					});

					// Handle all promises
					Promise.allSettled(promises).then(function(results) {
						var successCount = 0;
						var errorCount = 0;
						var errors = [];

						results.forEach(function(result, index) {
							if (result.status === 'fulfilled') {
								successCount++;
							} else {
								errorCount++;
								errors.push('Feature ' + Object.keys(storedData)[index] + ': ' + (result.reason.responseText || 'Unknown error'));
							}
						});

						// Show results
						var alertClass = errorCount === 0 ? 'alert-success' : (successCount === 0 ? 'alert-danger' : 'alert-warning');
						var icon = errorCount === 0 ? 'fa-check' : (successCount === 0 ? 'fa-times' : 'fa-exclamation-triangle');
						var message = '';

						if (errorCount === 0) {
							message = 'All ' + successCount + ' features saved successfully!';
							clearStoredData(source);
							window.location.href = "{{ path('widget_management_list') }}";
						} else if (successCount === 0) {
							message = 'Failed to save all features. Please try again.';
						} else {
							message = successCount + ' features saved successfully, ' + errorCount + ' failed.';
						}

						var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
							'<i class="fas ' + icon + '"></i> ' + message;

						if (errors.length > 0) {
							alertHtml += '<br><small>' + errors.join('<br>') + '</small>';
						}

						alertHtml += '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div>';

						// Show alert in the card body
						var cardBody = button.closest('.card').find('.card-body');
						cardBody.find('.alert').remove();
						cardBody.prepend(alertHtml);

						// Reset button
						button.prop('disabled', false);
						button.html(originalHtml);

						// Auto-dismiss after 5 seconds
						setTimeout(function() {
							cardBody.find('.alert').fadeOut();
						}, 5000);
					});
				});

				// Load existing stored data on page load
				{% for source, featureForms in forms %}
					var storedData = getStoredFormData('{{ source }}');
					if (Object.keys(storedData).length > 0) {
						savedForms['{{ source }}'] = storedData;
						showSaveAllButton('{{ source }}');
					}
				{% endfor %}
			});
		</script>
		 <script type="text/javascript">
		        $(".widget_enabled").on('change', function() {
		            
		            var action = $(this).prop('checked') ? "enable" : "disable";
		            var confirmation = confirm("Are you sure you want to " + action + " the widget?");
		
		            if (confirmation) { 
		                $.post("/widget_management/enable/{{ widget.id }}",
		                {
		                    enabled : $(this).prop('checked'),
		                    source: $(this).data('source'),
		                },
		                function(data, status) {
		                    var widgetStatus = (data == true) ? "enabled" : "disabled";
		                    if (status == "success") {
		                        // $('.enabeled-msg').html('<div class="alert alert-success">widget '+widgetStatus+' successfuly</span>');
		                    } else {
		                        $('.enabeled-msg').html('<div class="alert alert-danger">error: actvation faild</span>');
		                    }
		                });
		            }
		        });
		    </script>
		 <script>
		        var fieldId = "";
		        $(document).ready(function() {
		            $('.delete-media').on('click', function() {
		                fieldId = $(this).closest('.field-container').data('field-id'); 
		                $('[data-media-id="' + fieldId + '"]').val("");
		                $("#preview-"+ fieldId).attr('src', "");
		                $("#preview-"+ fieldId).hide();
		                $(this).hide();
		            });
		
		            $('.add-media').on('click', function() {
		                fieldId = $(this).closest('.field-container').data('field-id'); 
		                $('#mediaSearch-'+fieldId).val('');
		                if ( $(".model-image-picker").length ) {
		                    $(".model-image-picker").imagepicker({hide_select: true, show_label: true});   
		                    $("#preview-img-"+fieldId).on("click", function() {
		                        var selectedImg = $("#select-"+fieldId+" .image_picker_selector .selected .image_picker_image").attr('src');
		                        if ($.type(selectedImg) == 'string') {
		                            $('[data-media-id="' + fieldId + '"]').val(selectedImg);                         
		                            $("#preview-"+ fieldId).attr('src', selectedImg);
		                            $("#preview-"+ fieldId).show();
		                            $("#preview-"+ fieldId).removeClass('d-none');
		                            $("#delete-" + fieldId).removeClass('d-none');
		                            $("#delete-" + fieldId).show();
		                        } else {
		                            $("#delete-"+ fieldId).hide();
		                            $("#delete-"+ fieldId).addClass('d-none');
		                            $("#preview-"+ fieldId).hide();
		                            $("#preview-"+ fieldId).addClass('d-none');
		                        }
		                        $(".close").click();
		                    });
		                }
		                $('#mediaSearch-'+fieldId).on('input', function() {  // Listen for input changes
		                    var query = $(this).val().toLowerCase();;
		                    $('.image_picker_selector li').each(function() {
		                        var img = $(this).find('.image_picker_image');
		                        if (img.length > 0) {
		                            var mediaName = img.attr('data-name').toLowerCase();
		                            if (mediaName && mediaName.includes(query)) { 
		                                $(this).show();
		                            } else {
		                                $(this).hide();
		                            }
		                        }
		                    });
		                });
		            });
		        });
		    </script>
		 <script type="text/javascript" charset="utf8" src="{{ asset('js/image-picker/image-picker.js') }}"></script>
		<link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet"> <style type="text/css">
			.thumbnails li img {
				width: 180px;
				height: 180px;
			}
		</style>

		{{ include('widget/_multi_value_js.html.twig') }}
		 <script type="text/javascript" charset="utf8" src="{{ asset('js/multi-fields.js') }}"></script>

	{% endblock %}