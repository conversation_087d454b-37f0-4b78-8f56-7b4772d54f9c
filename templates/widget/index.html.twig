{% extends '_layout/layout.html.twig' %}

{% block title %}
	{{ 'widget_administration' | trans | capitalize }}
{% endblock %}

{% block content %}
	<div class="card">


		<div class="card-header">

			<h3 class="card-title" id="selected-menu" style="display:inline;padding-right:1%;">
				{% if sub_title|default(null) is not null %}
					<span>{{sub_title|trans|capitalize}}</span>
				{% endif %}
			</h3>

			<div class="d-flex float-right">

				<div class="btn-wrapper mx-1">
					<a href="{{ path('widget_admin_create') }}" class="btn btn-success">
						<i class="fas fa-plus"></i>
						{{ 'new_widget'|trans }}
					</a>
				</div>


				<div class="btn-wrapper mx-1">
					<form action="{{ path('widget_admin_import') }}" method="post" enctype="multipart/form-data" style="display: inline;">
						<input type="hidden" name="_csrf_token" value="{{ csrf_token('import') }}">
						<label for="upload-json" class="btn btn-block btn-info">
							<i class="fas fa-upload"></i>
							{{'import'| trans}}
						</label>
						<input type="file" id="upload-json" name="file" accept=".json,.gz" style="display: none;" onchange="this.form.submit()">
					</form>
				</div>

			</div>


		</div>
	</div>
	<div class="card-body">
		<div class="row">
			<div class="col-md-3 form-group" id="search_guid">
				<label for="widget_guid">{{ 'GUID'|trans }}</label>
			</div>
			<div class="col-md-3 form-group" id="search_name">
				<label for="widget_name">{{ 'name'|trans }}</label>
			</div>
			<div class="col-md-3 form-group" id="search_type">
				<label for="widget_type">{{ 'type'|trans }}</label>
			</div>


			<div class="table-responsive">
				<table class="table table table-striped dataTable dtr-inline" id="widgets_table">
					<thead>
						<th id="guid_column">GUID</th>
						<th id="name_column">{{ 'name' | trans }}</th>
						<th id="type_column">{{'type' | trans}}</th>
						<th class='actions'>{{'actions' | trans}}</th>
					</thead>
					<tbody>
						{% for widget in widgets %}
							<tr>
								<td>{{widget.wguid}}</td>
								<td>{{widget.name}}</td>
								<td>{{widget.type}}</td>
								<td>
									<div class="btn-group">
										{% if isSuperAdmin %}
											<div class="btn-wrapper mx-1">
												<a class="btn btn-block btn-primary form-control" href="{{ path('widget_management_download', {'id': widget.id}) }}" title="Download" download="file_name">
													<i class="fas fa-download mr-4"></i>
												</a>
											</div>
										{% endif %}
										<div class="btn-wrapper mx-1">
											<a href="{{ path('widget_admin_edit', {'id': widget.id}) }}" class="btn btn-warning">
												<i class="fas fa-edit"></i>
											</a>
										</div>

										<div class="btn-wrapper mx-1">
											<button type="button" class="btn btn-block btn-success" data-bs-toggle="modal" data-bs-target="#jsonViewerModal" data-resource-url="{{ path('widget_management_download', {'id': widget.id}) }}" onclick="loadJsonContent(this, '')">
												<i class="fas fa-eye"></i>
											</button>
										</div>
										<div class="btn-wrapper mx-1">
											<form action="{{ path('widget_admin_make_package', {'id': widget.id}) }}" method="post" style="display: inline;">
												<input type="hidden" name="_csrf_token" value="{{ csrf_token('widget_admin_make_package') }}">
												<button type="submit" class="btn btn-block btn-primary form-control">
													<i class="fas fa-truck"></i>
												</button>
											</form>
										</div>

										{% if (widget.widgetData|length == 0) and (widget.widgetFeatures|length == 0) and not widgetManager.isWidgetPublished(widget) %}
											<div class="btn-wrapper mx-1">
												<a href="#delete-widget-modal" class="btn btn-danger" data-widget-id="{{ widget.id }}" data-widget-name="{{ widget.name }}" data-toggle="tooltip" data-config-nbr="{{ (widget.widgetData|length + widget.widgetFeatures|length) }}">
													<i class="fas fa-trash"></i>
												</a>
											</div>
										{% else %}
											<div class="btn-wrapper mx-1">
												<a href="#" class="btn btn-danger disabled" title="Widget is already published">
													<i class="fas fa-trash"></i>
												</a>
											</div>

										{% endif %}
									</div>
								</td>
							</tr>
						{% endfor %}
					</tbody>
				</table>
			</div>
		</div>
	</div>
	{{ include('widget/_delete_modal.html.twig') }}

	{{ include('widget/json_viewer.html.twig') }}
{% endblock %}
{% block script %}
	{% include "widget/_dt_js.html.twig" %}
	{% include "widget/_delete_modal_js.html.twig" %}
{% endblock %}
