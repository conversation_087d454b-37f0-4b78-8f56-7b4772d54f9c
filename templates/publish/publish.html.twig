{% extends '_layout/layout.html.twig' %}

{% block style %}
	<!-- Select2 -->
	<link href="{{ asset('plugins/select2/css/select2.min.css') }}" rel="stylesheet" type="text/css">
	<link href="{{ asset('plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block title %}
	Reference Label Release
{% endblock %}

{% block content %}
	<div class="card">
	<div class="row">	    
	{{ form_start(form, {'attr': {'class':'sync-form'}}) }}
	<div class="card">
		{% include '_layout/form_card_header.html.twig' with{
            'title': 'title_settings' | trans
        }%}
		<div class="card-body Publish">
		{% if (profile.isSuperAdministrator) %}
			<div class="row">
				<div class="col-md-3">{{'brand' | trans }}</div>
				<div class="col-md-8 form-group">
					<div class="row">
						<div class="col-5 mt-1">
							<div id="select-container">
								{{ form_widget(form.brand)}}
								<div id="loader" class="load"></div>
								<div id="overlay" class="load"></div>
							</div>
						</div>
						<div class="col-2 mt-3 text-center align-middle align-items-center justify-content-center align-self-center">
							<button type="button" id="{{ form.brand.vars.id }}_rightSelected" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-chevron-right"></i>
							</button>
							<button type="button" id="{{ form.brand.vars.id }}_rightAll" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-forward"></i>
							</button>
							<br/>
							<button type="button" id="{{ form.brand.vars.id }}_leftSelected" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-chevron-left"></i>
							</button>
							<button type="button" id="{{ form.brand.vars.id }}_leftAll" class="w-p-10 mt-1 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-backward"></i>
							</button>
						</div>
						<div class="col-5 mt-1">
							{{ form_widget(form.brandList, {'id': form.brand.vars.id ~ '_to' }) }}
							{{ form_errors(form.brandList) }}
						</div>
					</div>
				</div>
			</div> 
		{% endif %}
		{% if (not profile.isCountryUser) %}
			<div class="row">
				<div class="col-md-3">{{'country' | trans }}</div>
				<div class="col-md-8 form-group">
					<div class="row">
						<div class="col-5 mt-1">
							<div id="select-container">
								{{ form_widget(form.country)}}
								<div id="loader" class="load"></div>
								<div id="overlay" class="load"></div>
							</div>
						</div>
						<div class="col-2 mt-3 text-center align-middle align-items-center justify-content-center align-self-center">
							<button type="button" id="{{ form.country.vars.id }}_rightSelected" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-chevron-right"></i>
							</button>
							<button type="button" id="{{ form.country.vars.id }}_rightAll" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-forward"></i>
							</button>
							<br/>
							<button type="button" id="{{ form.country.vars.id }}_leftSelected" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-chevron-left"></i>
							</button>
							<button type="button" id="{{ form.country.vars.id }}_leftAll" class="w-p-10 mt-1 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-backward"></i>
							</button>
						</div>
						<div class="col-5 mt-1">
							{{ form_widget(form.countryList, {'id': form.country.vars.id ~ '_to' }) }}
							{{ form_errors(form.countryList) }}
						</div>
					</div>
				</div>
			</div>
		{% endif %}
			<div class="row">
				<div class="col-md-3">{{'language' | trans }}</div>
				<div class="col-md-8 form-group">
					<div class="row">
						<div class="col-5 mt-1">
							<div id="select-container">
								{{ form_widget(form.language)}}
								<div id="loader" class="load"></div>
								<div id="overlay" class="load"></div>
							</div>
						</div>
						<div class="col-2 mt-3 text-center align-middle align-items-center justify-content-center align-self-center">
							<button type="button" id="{{ form.language.vars.id }}_rightSelected" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-chevron-right"></i>
							</button>
							<button type="button" id="{{ form.language.vars.id }}_rightAll" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-forward"></i>
							</button>
							<br/>
							<button type="button" id="{{ form.language.vars.id }}_leftSelected" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-chevron-left"></i>
							</button>
							<button type="button" id="{{ form.language.vars.id }}_leftAll" class="w-p-10 mt-1 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-backward"></i>
							</button>
						</div>
						<div class="col-5 mt-1">
							{{ form_widget(form.languageList, {'id': form.language.vars.id ~ '_to' }) }}
							{{ form_errors(form.languageList) }}
						</div>
					</div>
			</div>
		{% if (not profile.isCountryUser) %}
			<div class="row">
				<div class="col-md-3">{{'reference_language' | trans }}</div>
				<div class="col-md-8 form-group">
					<div class="row">
						<div class="col-5 mt-1">
							<div id="select-container">
								{{ form_widget(form.referenceLanguage)}}
								<div id="loader" class="load"></div>
								<div id="overlay" class="load"></div>
							</div>
						</div>
						<div class="col-2 mt-3 text-center align-middle align-items-center justify-content-center align-self-center">
							<button type="button" id="{{ form.referenceLanguage.vars.id }}_rightSelected" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-chevron-right"></i>
							</button>
							<button type="button" id="{{ form.referenceLanguage.vars.id }}_rightAll" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-forward"></i>
							</button>
							<br/>
							<button type="button" id="{{ form.referenceLanguage.vars.id }}_leftSelected" class="mt-1 w-p-10 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-chevron-left"></i>
							</button>
							<button type="button" id="{{ form.referenceLanguage.vars.id }}_leftAll" class="w-p-10 mt-1 btn btn-primary btn-block multiselect-btn-center">
								<i class="fa fa-backward"></i>
							</button>
						</div>
						<div class="col-5 mt-1">
							{{ form_widget(form.referenceLanguageList, {'id': form.referenceLanguage.vars.id ~ '_to' }) }}
							{{ form_errors(form.referenceLanguageList) }}
						</div>
					</div>
			</div>
		{% endif %}
			<div class = "row">
				</div>
					<div class="col-md-3">{{ 'source' | trans }}</div>
				<div class="col-md-8 form-group">
					{{ form_widget(form.source, {'attr': {'data-placeholder' : 'Source', 'class': 'form-control custom-select select2'}}) }}
					{{ form_errors(form.source) }}
				</div>
			</div>
		</div>
		<div class="card-footer">
			<button type="submit" id="submit" class="btn btn-success float-right">
				<i class="fas fa-upload mr-4"></i>
				 {{'release'| trans}}</button>
		</div>
	</div>
	{{ form_end(form) }}
{% endblock %}


{% block script %}
	{{ parent() }}
	<script src="{{ asset('js/multiselect.js') }}"></script>
	<script>
		jQuery(document).ready(function($) {
			{% if form.brand is defined %}
				$('#{{ form.brand.vars.id }}').multiselect({
					search: {
						left: '<input type="text" name="q" class="form-control mb-2" placeholder="Search..." autocomplete="none" />',
						right: '<input type="text" name="q" class="form-control mb-2" placeholder="Search..." autocomplete="none" />',
					},
					fireSearch: function(value) {
						return -1;
					},
					keepRenderingSort: true
				});
			{% endif %}
			{% if form.country is defined %}
				$('#{{ form.country.vars.id }}').multiselect({
					search: {
						left: '<input type="text" name="q" class="form-control mb-2" placeholder="Search..." autocomplete="none" />',
						right: '<input type="text" name="q" class="form-control mb-2" placeholder="Search..." autocomplete="none" />',
					},
					fireSearch: function(value) {
						return -1;
					},
					keepRenderingSort: true
				});	
				$('#{{ form.referenceLanguage.vars.id }}').multiselect({
				keepRenderingSort: true
				});
			{% endif %}
			$('#{{ form.language.vars.id }}').multiselect({
				search: {
					left: '<input type="text" name="q" class="form-control mb-2" placeholder="Search..." autocomplete="none" />',
					right: '<input type="text" name="q" class="form-control mb-2" placeholder="Search..." autocomplete="none" />',
				},
				fireSearch: function(value) {
					return -1;
				},
				keepRenderingSort: true
			});
		});
	</script>
{% endblock script %}