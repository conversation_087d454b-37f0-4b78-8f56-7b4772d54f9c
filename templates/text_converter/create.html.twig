{% extends '_layout/layout.html.twig' %}
{% block title %}
	{{ 'text_converter'|trans }}
{% endblock %}
{% block style %}
	{# <link href="{{ asset('css/ckeditor/ckeditor.css') }}" rel="stylesheet" type="text/css"> #}
	<style>
		.form-wrapper {
			max-width: auto;
			max-height: auto;
			margin: 0 auto;
		}
		/* Style for the editor container */
		.editor-container {
			border: 1px solid #ddd;
			border-radius: 4px;
			margin-bottom: 20px;
		}
		/* Hide the original textarea but keep it in the DOM */
		#form_content {
			display: none;
		}
	</style>
{% endblock %}

{% block content %}
	{% include '_layout/form_card_header.html.twig' with{
            'title': 'Static Pages' | trans
     }%}
	<div class="mt-4">
		<div class="form-wrapper h-100">
			{{ form_start(form) }}

			<div
				class="card mt-4">
				<!-- Submit Button -->
				<div class="text-end mt-3 p-4">
					<input
					type="hidden" name="action" id="action_input" value="">

					<!-- Preview Button -->
					<button type="submit" onclick="setAction('preview')" name="action" value="preview" class="btn btn-primary me-2" formnovalidate>
						<i class="fas fa-eye me-2"></i>
						Preview
					</button>

					<!-- Save Button -->
					<button type="submit" onclick="setAction('save')" name="action" value="save" class="btn btn-success me-2">
						<i class="fas fa-save me-2"></i>
						Save
					</button>

					<!-- Publish Button -->
					<button type="submit" onclick="setAction('publish')" name="action" value="publish" class="btn btn-warning">
						<i class="fas fa-paper-plane me-2"></i>
						Publish
					</button>
				</div>
				<!-- Card for Title, Brand, and Language -->
				<div class="card shadow-sm mb-4">
					<div class="card-body">
						<div
							class="row">
							<!-- Title (Full Width) -->
							<div class="col-12 mb-3">
								{{ form_row(form.page_title, { 
									'attr': {'class': 'form-control'},
									'row_attr': {'class': 'form-group'}
								}) }}
							</div>

							<!-- Channel & Language (Side by Side) -->
							<div class="col-md-6">
								{{ form_row(form.channel, { 
									'attr': {'class': 'form-control'},
									'row_attr': {'class': 'form-group'}
								}) }}
							</div>
							<div class="col-md-6">
								{{ form_row(form.language, { 
									'attr': {'class': 'form-control'},
									'row_attr': {'class': 'form-group'}
								}) }}
							</div>
						</div>
					</div>
				</div>

				<!-- Card for Content Editor -->
				<div class="card shadow-sm">
					<div class="card-body">
						<div
							class="mb-3">
							<!-- CKEditor Instance -->
							<textarea id="editor"></textarea>
						</div>
						<!-- Hidden Input for Storing Editor Content -->
						{{ form_widget(form.content, { 'id': 'form_content', 'attr': {'class': 'd-none'} }) }}
					</div>
				</div>
			</div>

			{{ form_end(form) }}
		</div>
	</div>
{% endblock %}
{% block script %}
	{{ parent() }}

	 <script src="https://cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>
	{#          <script type="text/javascript" charset="utf8" src="{{ asset('/js/ckeditor/ckeditor.js') }}"></script>
#}
	 <script>
				     function setAction(actionValue) {
				        document.getElementById('action_input').value = actionValue;
				    }
									            document.addEventListener('DOMContentLoaded', function() {
									                // Initialize CKEditor on our visible textarea
									                const editor = CKEDITOR.replace('editor', {
									                    toolbar: [
									                        { name: 'document', items: ['Source'] },
									                        { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat'] },
									                        { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote'] },
									                        { name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
									                        { name: 'colors', items: ['TextColor', 'BGColor'] },
									                        { name: 'align', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },
									                        { name: 'links', items: ['Link', 'Unlink'] },
									                        { name: 'insert', items: ['Image', 'Table', 'HorizontalRule', 'SpecialChar'] },
									                        { name: 'tools', items: ['Maximize'] }
									                    ],
									                    height: 800,
									                    // Ensure content syncs with form field
														extraAllowedContent: 'div[id,style];span[id,style];p[id,style];h1[id,style];h2[id,style];h3[id,style];h4[id,style];h5[id,style];h6[id,style]',
														allowedContent: true,
									                    on: {
									                        change: function() {
									                            updateFormContent();
									                        },
									                        blur: function() {
									                            updateFormContent();
									                        }
									                    }
									                });
									                // Function to sync editor content with form field
									                function updateFormContent() {
									                    document.getElementById('form_content').value = editor.getData();
									                }
									                // Update form content before submission
									                document.querySelector('form').addEventListener('submit', function() {
									                    updateFormContent();
									                    console.log('Submitting content:', document.getElementById('form_content').value);
									                });
									    
									                // Initialize with any existing content
									                if (document.getElementById('form_content').value) {
									                    editor.setData(document.getElementById('form_content').value);
									                }
									            });
											</script>
{% endblock %}
